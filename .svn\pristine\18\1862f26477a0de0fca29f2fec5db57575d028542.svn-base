{include file="header.htm"}

<style>
.table-responsive {
    overflow-x: auto;
}
.category-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.category-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.category-actions .btn-group {
    display: inline-block;
}
.dropdown-toggle::after {
    display: none !important;
}
.dropdown-item {
    text-decoration: none;
    padding: 0.25rem 1rem;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}
.nav-link.active {
    background-color: #3490dc;
    color: #fff;
}

/* 淡色按钮样式 */
.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
}
.btn-light-warning {
    background-color: #fff8e6;
    color: #ffa500;
    border: 1px solid #ffe6b3;
}
.btn-light-warning:hover {
    background-color: #fff0d1;
    color: #cc8400;
}
.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}
.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
}
.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}
.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
}

/* 筛选标签样式 */
.filter-tag {
    display: inline-block;
    padding: 6px 10px;
    font-size: 12px;
    font-weight: normal;
    color: #fff;
    text-decoration: none;
    border-radius: 4px;
    margin-right: 5px;
    margin-bottom: 5px;
}
.filter-tag.active-all { background-color: #007bff; }
.filter-tag.inactive-all { background-color: #6c757d; }

/* 分页样式 */
.simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination-btn {
    display: inline-block;
    padding: 5px 12px;
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s;
}
.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #ccc;
}
.pagination-btn.active {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}
.pagination-btn.disabled {
    color: #aaa;
    background: #f8f8f8;
    cursor: not-allowed;
}

/* 固定表格列宽 */
.table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}
.table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* 设置每列的固定宽度 */
.table .col-checkbox { width: 40px; }
.table .col-name { width: 380px; }
.table .col-parent { width: 120px; }
.table .col-pinyin { width: 120px; }
.table .col-status { width: 80px; }
.table .col-sort { width: 80px; }
.table .col-actions { width: 200px; }
</style>

<!-- 消息提示 -->
{if $message}
<div class="alert alert-success">{$message}</div>
{/if}
{if $error}
<div class="alert alert-danger">{$error}</div>
{/if}

<!-- 分类管理 -->
<div class="card mb-4">
    <div class="card-body">
        <!-- 快速操作按钮 -->
        <div style="margin-bottom: 20px;">
            <div style="display: flex; flex-wrap: wrap; gap: 10px; align-items: center;">
                <a href="category.php?action=add" class="btn btn-sm btn-light-success">
                    <i class="fas fa-plus"></i>
                    添加分类
                </a>
                <a href="category.php?action=batch" class="btn btn-sm btn-light-secondary">
                    <i class="fas fa-layer-group"></i>
                    批量添加
                </a>
                {if isset($current_parent_name)}
                <div style="margin-left: auto; padding: 8px 12px; background-color: #f8f9fa; border-radius: 4px; border-left: 4px solid #1b68ff;">
                    <span style="color: #6c757d; font-size: 13px;">当前位置: </span>
                    <a href="category.php?parent_id=-1" style="color: #1b68ff; text-decoration: none; margin: 0 5px; font-weight: 500;">一级分类</a>
                    <i class="fas fa-chevron-right" style="color: #6c757d; font-size: 12px;"></i>
                    <span style="margin: 0 5px; font-weight: 600; color: #333;">{$current_parent_name}的子分类</span>
                </div>
                {/if}
            </div>
        </div>

        <!-- 搜索筛选 -->
        <div style="margin-bottom: 20px;">
            <form action="category.php" method="get" style="display: flex; align-items: center; gap: 10px; flex-wrap: wrap;">
                <input type="hidden" name="view" value="list">
                <input type="hidden" name="parent_id" value="-1">

                <div style="display: flex; align-items: center; gap: 8px;">
                    <label style="font-weight: 500; color: #333; white-space: nowrap;">关键字:</label>
                    <input type="text" name="keyword" value="{$keyword}" placeholder="搜索分类名称..."
                           style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; width: 200px;">
                </div>

                <div style="display: flex; align-items: center; gap: 8px;">
                    <label style="font-weight: 500; color: #333; white-space: nowrap;">状态:</label>
                    <select name="status" style="padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; width: 120px;">
                        <option value="-1" {if $status == -1}selected{/if}>全部</option>
                        <option value="1" {if $status == 1}selected{/if}>启用</option>
                        <option value="0" {if $status == 0}selected{/if}>禁用</option>
                    </select>
                </div>

                <div style="display: flex; gap: 8px;">
                    <button type="submit" class="btn btn-sm btn-light-primary">
                        <i class="fas fa-search"></i>
                        搜索
                    </button>
                    <a href="category.php?view=list&parent_id=-1" class="btn btn-sm btn-light-secondary">
                        <i class="fas fa-undo"></i>
                        重置
                    </a>
                </div>
            </form>
        </div>

        <!-- 分类列表 -->
        <form id="categoryForm" action="category.php?action=save_changes" method="post">
            <input type="hidden" name="return_parent_id" value="{$parent_id}">
            <div class="table-responsive">
                <table class="table table-vcenter table-bordered table-hover">
                    <thead>
                        <tr>
                            <th class="col-checkbox"></th>
                            <th class="col-name">名称</th>
                            <th class="col-parent">所属分类</th>
                            <th class="col-pinyin">标识符</th>
                            <th class="col-status">状态</th>
                            <th class="col-sort">排序</th>
                            <th class="col-actions" style="text-align: right;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        {if empty($categories)}
                        <tr>
                            <td colspan="7" class="text-center">暂无分类数据</td>
                        </tr>
                        {else}
                        {loop $categories $category}
                        <tr>
                            <td>
                                <input type="checkbox" name="category_ids[]" class="category-checkbox" value="{$category.id}">
                            </td>
                            <td title="{$category.name}" style="font-weight: 500;">
                                <div style="display: flex; align-items: center; gap: 5px; overflow: hidden;">
                                    <span style="overflow: hidden; text-overflow: ellipsis; display: block;">
                                        {if $category.level > 1}
                                            {$category.name}
                                        {else}
                                            <strong>{$category.name}</strong>
                                        {/if}
                                    </span>
                                    {if $category.level == 1}
                                    <a href="category.php?parent_id={$category.id}" class="btn btn-sm btn-light-info" title="查看子类">子类</a>
                                    {/if}
                                    <a href="../category.php?id={$category.id}" target="_blank" class="btn btn-sm btn-light-warning" title="查看前台">前台</a>
                                </div>
                            </td>
                            <td>{if $category.parent_id > 0}{$category.parent_name|default:'无'}{else}-{/if}</td>
                            <td>
                                <input type="text" name="pinyin[{$category.id}]" value="{$category.pinyin}"
                                       style="width: 100px; padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px;"
                                       maxlength="50">
                            </td>
                            <td>
                                {if $category.status == 1}
                                <span style="display: inline-block; padding: 2px 6px; font-size: 11px; background-color: #e6ffe6; color: #00aa00; border-radius: 3px;">启用</span>
                                {else}
                                <span style="display: inline-block; padding: 2px 6px; font-size: 11px; background-color: #ffe6e6; color: #ff3333; border-radius: 3px;">禁用</span>
                                {/if}
                            </td>
                            <td>
                                <input type="text" name="sort[{$category.id}]" value="{$category.sort_order}"
                                       style="width: 60px; padding: 6px 12px; border: 1px solid #ddd; border-radius: 4px; font-size: 14px; text-align: center;"
                                       maxlength="5">
                            </td>
                            <td>
                                <div class="category-actions">
                                    <a href="category.php?action=edit&id={$category.id}" class="btn btn-sm btn-light-primary">编辑</a>
                                    {if $category.status == 1}
                                    <a href="category.php?action=toggle_status&id={$category.id}" class="btn btn-sm btn-light-warning"
                                       onclick="return confirm('确定要禁用此分类吗？');">禁用</a>
                                    {else}
                                    <a href="category.php?action=toggle_status&id={$category.id}" class="btn btn-sm btn-light-success">启用</a>
                                    {/if}
                                    <a href="category.php?action=delete&id={$category.id}&return_parent_id={$parent_id}"
                                       class="btn btn-sm btn-light-danger"
                                       onclick="return confirm('确定要删除此分类吗？删除前请确保没有关联的信息和子分类！');">删除</a>
                                </div>
                            </td>
                        </tr>
                        {/loop}
                        {/if}
                    </tbody>
                </table>
            </div>

            <!-- 分页 -->
            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- 左侧全选和批量操作 -->
                <div style="margin-bottom: 15px;">
                    <label style="margin-right: 10px; display: inline-flex; align-items: center; cursor: pointer;">
                        <input type="checkbox" id="selectAll" style="margin-right: 5px;"> 全选
                    </label>
                    <button type="button" id="batchDeleteBtn" class="btn btn-sm btn-light-danger" style="height: 32px; line-height: 1; padding: 0 12px;">批量删除</button>
                    <button type="button" id="saveChangesBtn" class="btn btn-sm btn-light-primary" style="height: 32px; line-height: 1; padding: 0 12px;">保存修改</button>
                </div>

                <!-- 分页 -->
                <div style="flex: 1; text-align: right;">
                    {if isset($pagination) && $pagination.total_pages > 1}
                    <div>
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            {if $pagination.current_page > 1}
                            <a href="{$pagination.previous_link}" class="pagination-btn">上一页</a>
                            {else}
                            <span class="pagination-btn disabled">上一页</span>
                            {/if}

                            {loop $pagination.page_links $page $link}
                            <a href="{$link}" class="pagination-btn {if $page == $pagination.current_page}active{/if}">{$page}</a>
                            {/loop}

                            {if $pagination.current_page < $pagination.total_pages}
                            <a href="{$pagination.next_link}" class="pagination-btn">下一页</a>
                            {else}
                            <span class="pagination-btn disabled">下一页</span>
                            {/if}
                        </div>
                        <div style="margin-top: 10px; color: #666; font-size: 14px; text-align: right;">
                            第 {$pagination.current_page} 页，共 {$pagination.total_pages} 页
                        </div>
                    </div>
                    {/if}
                </div>
            </div>
        </form>
    </div>
</div>

<!-- 批量删除表单 -->
<form id="deleteForm" action="category.php?action=batch_delete" method="post" style="display:none;">
    <input type="hidden" name="return_parent_id" value="{$parent_id}">
    <!-- 此处将由JavaScript动态添加选中的分类ID -->
</form>

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选功能
    const selectAllCheckbox = document.getElementById('selectAll');
    const categoryCheckboxes = document.querySelectorAll('.category-checkbox');

    if (selectAllCheckbox) {
        selectAllCheckbox.addEventListener('change', function() {
            categoryCheckboxes.forEach(function(checkbox) {
                checkbox.checked = selectAllCheckbox.checked;
            });
        });
    }

    // 单个复选框变化时更新全选状态
    categoryCheckboxes.forEach(function(checkbox) {
        checkbox.addEventListener('change', function() {
            const allChecked = Array.from(categoryCheckboxes).every(cb => cb.checked);
            const noneChecked = Array.from(categoryCheckboxes).every(cb => !cb.checked);

            if (selectAllCheckbox) {
                selectAllCheckbox.checked = allChecked;
                selectAllCheckbox.indeterminate = !allChecked && !noneChecked;
            }
        });
    });
    
    // 添加输入框的数字限制
    var sortInputs = document.querySelectorAll('.sort-input');
    sortInputs.forEach(function(input) {
        // 限制只能输入数字
        input.addEventListener('keypress', function(e) {
            if (!/[\d]/.test(e.key)) {
                e.preventDefault();
            }
        });

        // 失去焦点时处理
        input.addEventListener('blur', function() {
            if (this.value === '') {
                this.value = '0';
            }
            var num = parseInt(this.value);
            if (isNaN(num) || num < 0) {
                this.value = '0';
            }
        });
    });
});

    // 批量删除按钮事件
    const batchDeleteBtn = document.getElementById('batchDeleteBtn');
    if (batchDeleteBtn) {
        batchDeleteBtn.addEventListener('click', function() {
            const checkedBoxes = document.querySelectorAll('.category-checkbox:checked');
            if (checkedBoxes.length === 0) {
                alert('请选择要删除的分类');
                return;
            }

            if (confirm('确定删除选中的 ' + checkedBoxes.length + ' 个分类吗？删除前请确保没有关联的信息和子分类！')) {
                const form = document.getElementById('categoryForm');
                form.action = 'category.php?action=batch_delete';
                form.submit();
            }
        });
    }

    // 保存修改按钮事件
    const saveChangesBtn = document.getElementById('saveChangesBtn');
    if (saveChangesBtn) {
        saveChangesBtn.addEventListener('click', function() {
            const form = document.getElementById('categoryForm');

            // 检查拼音输入
            const pinyinInputs = document.querySelectorAll('input[name^="pinyin["]');
            for (let input of pinyinInputs) {
                if (input.value.trim() === '') {
                    alert('拼音标识不能为空');
                    input.focus();
                    return;
                }
            }

            // 检查排序输入
            const sortInputs = document.querySelectorAll('input[name^="sort["]');
            for (let input of sortInputs) {
                if (input.value.trim() === '' || isNaN(parseInt(input.value))) {
                    input.value = '0';
                }
            }

            form.action = 'category.php?action=save_changes';
            form.submit();
        });
    }

});
</script>

{include file="footer.htm"} 