<?php if(null !== ($posts ?? null) && !empty($posts)): ?>
    <?php if(null !== ($posts ?? null) && is_array($posts)): foreach($posts as $post): ?>
    <?php
    // 计算有效期
    if (!empty($post['expired_at'])) {
        $days = getRemainingDaysInt($post['expired_at']);
        $validity = $days > 0 ? $days.'天' : '已过期';
    } else {
        $validity = '长期';
    }
    ?>
    <!-- 文本视图项目 -->
    <div class="list-item <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?>is-top<?php endif; ?>">
        <div class="item-title">
            <?php if ($validity == '已过期'): ?>
            <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="expired"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
            <?php else: ?>
            <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
            <?php endif; ?>
            <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?>
            <span class="top-tag">顶</span>
            <?php endif; ?>
        </div>
        <div class="item-meta">
            <span class="meta-item">
                <i class="fas fa-map-marker-alt"></i>
                <?php echo null !== ((null !== ($post ?? null)) ? ($post['region_name']) : null) && ((null !== ($post ?? null)) ? ($post['region_name']) : null) !== "" ? (null !== ($post ?? null)) ? ($post['region_name']) : null : '未知地区'; ?>
            </span>
            <span class="meta-item">
                <i class="fas fa-calendar-alt"></i>
                <?php echo $validity; ?>
            </span>
            <span class="meta-item">
                <i class="far fa-clock"></i>
                <?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?>
            </span>
        </div>
    </div>

    <!-- 图片视图项目 -->
    <div class="post-item <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?>is-top<?php endif; ?>">
        <div class="post-image">
            <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('image_url', $post) && !empty($post['image_url'])): ?>
            <img src="<?php echo (isset($post['image_url'])) ? $post['image_url'] : ""; ?>" alt="<?php echo (isset($post['title'])) ? $post['title'] : ""; ?>">
            <?php else: ?>
            <img src="/static/images/no-image.png" alt="无图片">
            <?php endif; ?>
        </div>
        <div class="post-content">
            <div class="post-title">
                <?php if(null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_category', $post) && !empty($post['is_top_category']) || null !== ($post ?? null) && is_array($post) && array_key_exists('is_top_subcategory', $post) && !empty($post['is_top_subcategory'])): ?><span class="top-dot"></span><?php endif; ?>
                <?php
                $days = getRemainingDaysInt($post['expired_at']);
                if (!empty($post['expired_at']) && $days <= 0):
                ?>
                <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html" class="post-expired"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
                <?php else: ?>
                <a href="/<?php echo (isset($category['pinyin'])) ? $category['pinyin'] : ""; ?>/<?php echo (isset($post['id'])) ? $post['id'] : ""; ?>.html"><?php echo (isset($post['title'])) ? $post['title'] : ""; ?></a>
                <?php endif; ?>
            </div>
            <div class="post-meta">
                <?php echo null !== ((null !== ($post ?? null)) ? ($post['updated_at']) : null) ? friendlyTime((null !== ($post ?? null)) ? ($post['updated_at']) : null) : ""; ?>
                <?php
                if (!empty($post['expired_at'])) {
                    echo ' · ';
                    $days = getRemainingDaysInt($post['expired_at']);
                    echo $days > 0 ? $days.'天' : '已过期';
                }
                ?>
            </div>
        </div>
    </div>
    <?php endforeach; endif; ?>
<?php endif; ?>