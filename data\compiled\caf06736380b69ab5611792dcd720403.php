<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php if(null !== ($page_title ?? null)): ?><?php echo $page_title ?? ""; ?> - <?php endif; ?>分类信息网站后台管理</title>
    <link href="../static/font-awesome/css/all.min.css" rel="stylesheet">
    <link href="static/css/admin_clean.css?v=<?php echo time(); ?>" rel="stylesheet">
    <link href="static/css/pagination.css" rel="stylesheet">
    <link href="../static/css/image-compress.css" rel="stylesheet">
</head>
<body>
    <div class="wrapper" id="wrapper">
        <!-- 侧边栏 -->
        <div class="sidebar" id="sidebar">
            <div class="sidebar-header">
                <a href="index.php" class="logo">
                    <i class="fas fa-cube"></i>
                    <span>管理系统</span>
                </a>
            </div>
            <!-- 侧边栏菜单 -->
<div class="menu-item <?php if($current_page == 'index'): ?>active<?php endif; ?>">
    <a href="index.php">
        <i class="fas fa-home"></i>
        <span>控制面板</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'category'): ?>active<?php endif; ?>">
    <a href="category.php">
        <i class="fas fa-list"></i>
        <span>分类管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'region'): ?>active<?php endif; ?>">
    <a href="region.php">
        <i class="fas fa-map-marker-alt"></i>
        <span>区域管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'info'): ?>active<?php endif; ?>">
    <a href="info.php">
        <i class="fas fa-file-alt"></i>
        <span>信息管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news_category'): ?>active<?php endif; ?>">
    <a href="news_category.php">
        <i class="fas fa-newspaper"></i>
        <span>新闻栏目</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'news'): ?>active<?php endif; ?>">
    <a href="news.php">
        <i class="fas fa-edit"></i>
        <span>新闻管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'report'): ?>active<?php endif; ?>">
    <a href="report.php">
        <i class="fas fa-flag"></i>
        <span>举报管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'admin'): ?>active<?php endif; ?>">
    <a href="admin.php">
        <i class="fas fa-user-shield"></i>
        <span>管理员管理</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'operation_logs'): ?>active<?php endif; ?>">
    <a href="operation_logs.php">
        <i class="fas fa-history"></i>
        <span>操作日志</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'setting'): ?>active<?php endif; ?>">
    <a href="setting.php">
        <i class="fas fa-cog"></i>
        <span>系统设置</span>
    </a>
</div>

<div class="menu-item <?php if($current_page == 'cache_manager'): ?>active<?php endif; ?>">
    <a href="cache_manager.php">
        <i class="fas fa-memory"></i>
        <span>缓存管理</span>
    </a>
</div>



<div class="menu-item <?php if($current_page == 'db_backup'): ?>active<?php endif; ?>">
    <a href="db_backup.php">
        <i class="fas fa-database"></i>
        <span>数据库备份</span>
    </a>
</div>
        </div>

        <!-- 顶部导航 -->
<div class="top-nav">
    <div class="nav-left">
        <div class="toggle-sidebar" id="toggle-sidebar">
            <i class="fas fa-bars"></i>
        </div>
        <div class="breadcrumb">
            <span class="admin-badge"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <i class="fas fa-chevron-right"></i>
            <span>控制台</span>
            <?php if(null !== ($breadcrumb ?? null)): ?>
            <i class="fas fa-chevron-right"></i>
            <span><?php echo $breadcrumb ?? ""; ?></span>
            <?php endif; ?>
        </div>
    </div>
    <div class="nav-right">
        <div class="nav-item" id="clear-cache-btn" title="清理所有缓存">
            <i class="fas fa-trash-alt"></i>
        </div>
        <div class="nav-item" title="前台首页">
            <a href="../" target="_blank" style="color:inherit;text-decoration:none;">
                <i class="fas fa-home"></i>
            </a>
        </div>
        <div class="user-item">
            <div class="user-avatar"><i class="fas fa-user"></i></div>
            <span class="user-name"><?php echo (isset($admin['username'])) ? $admin['username'] : ""; ?></span>
            <a href="logout.php" class="logout-link" title="退出登录">
                <i class="fas fa-sign-out-alt"></i>
            </a>
        </div>
    </div>
</div>

<!-- 清理缓存功能的遮罩层和对话框 -->
<div id="cache-overlay" style="display:none; position:fixed; top:0; left:0; width:100%; height:100%; background:rgba(0,0,0,0.5); z-index:2000;"></div>
<div id="cache-modal" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#fff; border-radius:8px; box-shadow:0 4px 20px rgba(0,0,0,0.2); width:300px; padding:20px; z-index:2001;">
    <div style="margin-bottom:15px; font-size:16px; font-weight:600;">确认清理缓存</div>
    <p style="margin-bottom:20px; font-size:14px; color:#666;">此操作将清理所有缓存，包括：</p>
    <ul style="margin-bottom:20px; padding-left:20px; font-size:14px; color:#666;">
        <li>页面缓存</li>
        <li>数据缓存</li>
        <li>模板编译文件</li>
    </ul>
    <div style="display:flex; justify-content:flex-end; gap:10px;">
        <button id="cancel-clear-cache" style="padding:8px 16px; border:1px solid #ddd; background:#fff; border-radius:4px; cursor:pointer;">取消</button>
        <button id="confirm-clear-cache" style="padding:8px 16px; border:none; background:#dc3545; color:#fff; border-radius:4px; cursor:pointer;">确认清理</button>
    </div>
</div>

<!-- 成功提示框 -->
<div id="success-toast" style="display:none; position:fixed; top:50%; left:50%; transform:translate(-50%,-50%); background:#28a745; color:#fff; padding:15px 25px; border-radius:6px; box-shadow:0 4px 12px rgba(0,0,0,0.15); z-index:2002; font-size:14px;">
    <i class="fas fa-check-circle" style="margin-right:8px;"></i>
    缓存清理成功！
</div>

<script>
    document.addEventListener('DOMContentLoaded', function() {
        const clearCacheBtn = document.getElementById('clear-cache-btn');
        const cacheOverlay = document.getElementById('cache-overlay');
        const cacheModal = document.getElementById('cache-modal');
        const cancelClearCache = document.getElementById('cancel-clear-cache');
        const confirmClearCache = document.getElementById('confirm-clear-cache');
        const successToast = document.getElementById('success-toast');
        
        if (clearCacheBtn && cacheOverlay && cacheModal) {
            clearCacheBtn.addEventListener('click', function() {
                cacheOverlay.style.display = 'block';
                cacheModal.style.display = 'block';
            });
            
            cancelClearCache.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            cacheOverlay.addEventListener('click', function() {
                cacheOverlay.style.display = 'none';
                cacheModal.style.display = 'none';
            });
            
            confirmClearCache.addEventListener('click', function() {
                // 发送清理缓存请求
                const xhr = new XMLHttpRequest();
                xhr.open('POST', 'cache_manager.php', true);
                xhr.setRequestHeader('Content-Type', 'application/x-www-form-urlencoded');
                
                confirmClearCache.innerHTML = '清理中...';
                confirmClearCache.disabled = true;
                
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === 4) {
                        cacheOverlay.style.display = 'none';
                        cacheModal.style.display = 'none';
                        
                        if (xhr.status === 200) {
                            // 显示成功提示
                            successToast.style.display = 'block';
                            successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                            }, 100);
                            
                            setTimeout(function() {
                                successToast.style.transform = 'translate(-50%, -50%) scale(0.8)';
                                setTimeout(function() {
                                    successToast.style.display = 'none';
                                    successToast.style.transform = 'translate(-50%, -50%) scale(1)';
                                }, 300);
                            }, 2000);
                        }
                        
                        confirmClearCache.disabled = false;
                        confirmClearCache.innerHTML = '确认清理';
                    }
                };
                
                xhr.send('action=clear_cache&type=all');
            });
        }
    });
</script>


        <!-- 主内容区 (开始) -->
        <div class="main-content">

<script>
    document.addEventListener('DOMContentLoaded', function() {
        // 侧边栏折叠功能
        const toggleSidebar = document.getElementById('toggle-sidebar');
        const sidebar = document.getElementById('sidebar');
        const wrapper = document.getElementById('wrapper');
        
        if (toggleSidebar && sidebar && wrapper) {
            toggleSidebar.addEventListener('click', function() {
                sidebar.classList.toggle('collapsed');
                wrapper.classList.toggle('collapsed');
                localStorage.setItem('sidebar_collapsed', sidebar.classList.contains('collapsed'));
            });
            
            // 恢复侧边栏状态
            const isCollapsed = localStorage.getItem('sidebar_collapsed') === 'true';
            if (isCollapsed) {
                sidebar.classList.add('collapsed');
                wrapper.classList.add('collapsed');
            }
        }
        
        // 设置当前页面的菜单项为激活状态
        const currentPath = window.location.pathname;
        const menuItems = document.querySelectorAll('.menu-item a');
        
        menuItems.forEach(function(item) {
            const href = item.getAttribute('href');
            if (href) {
                // 检查完整路径匹配
                if (currentPath.endsWith(href)) {
                    const menuItem = item.closest('.menu-item');
                    if (menuItem) {
                        menuItem.classList.add('active');
                    }
                    
                    foundActive = true;
                }
            }
        });
        
        // 如果没有找到完全匹配的，尝试部分匹配
        if (!foundActive) {
            const pathParts = currentPath.split('/');
            const filename = pathParts[pathParts.length - 1];
            
            if (filename) {
                menuItems.forEach(function(item) {
                    const href = item.getAttribute('href');
                    if (href && href.includes(filename.split('.')[0])) {
                        const menuItem = item.closest('.menu-item');
                        if (menuItem) {
                            menuItem.classList.add('active');
                        }
                    }
                });
            }
        }
        
        // 保存当前激活的菜单项
        menuItems.forEach(function(item) {
            item.addEventListener('click', function() {
                const href = this.getAttribute('href');
                if (href) {
                    localStorage.setItem('active_menu_item', href);
                }
            });
        });
    });
</script>
 

<style>
.table-responsive {
    overflow-x: auto;
}
.info-actions {
    display: flex;
    flex-wrap: nowrap;
    justify-content: flex-end;
    gap: 5px;
    min-width: 120px;
}
.info-actions .btn {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    white-space: nowrap;
    text-decoration: none !important;
}
.info-actions .btn-group {
    display: inline-block;
}
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}
.nav-link {
    text-decoration: none !important;
}
.nav-link.active {
    background-color: #3490dc;
    color: #fff;
}

/* 淡色按钮样式 */
.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}
.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
}
.btn-light-warning {
    background-color: #fff8e6;
    color: #ffa500;
    border: 1px solid #ffe6b3;
}
.btn-light-warning:hover {
    background-color: #fff0d1;
    color: #cc8400;
}
.btn-light-danger {
    background-color: #ffe6e6;
    color: #ff3333;
    border: 1px solid #ffb3b3;
}
.btn-light-danger:hover {
    background-color: #ffd1d1;
    color: #cc0000;
}
.btn-light-info {
    background-color: #e6f7ff;
    color: #00aaff;
    border: 1px solid #b3e0ff;
}
.btn-light-info:hover {
    background-color: #d1f0ff;
    color: #0088cc;
}
.btn-light-success {
    background-color: #e6ffe6;
    color: #00aa00;
    border: 1px solid #b3ffb3;
}
.btn-light-success:hover {
    background-color: #d1ffd1;
    color: #008800;
}
.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}
.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
}

/* 固定表格列宽 */
.table {
    width: 100%;
    table-layout: fixed;
    white-space: nowrap;
}
.table th {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
.table td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}
/* 设置每列的固定宽度 */
.table .col-checkbox { width: 40px; }
.table .col-id { width: 60px; }
.table .col-post-id { width: 80px; }
.table .col-post-title { width: 250px; }
.table .col-type { width: 100px; }
.table .col-content { width: 250px; }
.table .col-contact { width: 120px; }
.table .col-time { width: 120px; }
.table .col-status { width: 80px; }
.table .col-actions { width: 180px; }

/* 分页样式 */
.simple-pagination {
    display: flex;
    justify-content: center;
    align-items: center;
    flex-wrap: wrap;
    gap: 5px;
}
.pagination-btn {
    display: inline-block;
    padding: 5px 12px;
    background: #fff;
    border: 1px solid #ddd;
    color: #333;
    text-decoration: none;
    border-radius: 3px;
    transition: all 0.2s;
}
.pagination-btn:hover {
    background: #f8f9fa;
    border-color: #ccc;
}
.pagination-btn.active {
    background: #1b68ff;
    color: white;
    border-color: #1b68ff;
}
.pagination-btn.disabled {
    color: #aaa;
    background: #f8f8f8;
    cursor: not-allowed;
}

/* 选项卡样式 */
.nav-tabs {
    display: flex;
    border-bottom: 2px solid #e9ecef;
    margin-bottom: 20px;
    background-color: #f8f9fa;
    border-radius: 6px 6px 0 0;
    padding: 0;
    overflow-x: auto;
}

.nav-tab {
    display: flex;
    align-items: center;
    padding: 12px 20px;
    color: #666;
    text-decoration: none;
    border-bottom: 3px solid transparent;
    transition: all 0.2s;
    white-space: nowrap;
    font-weight: 500;
    font-size: 14px;
    position: relative;
}

.nav-tab:hover {
    color: #1b68ff;
    background-color: #f0f8ff;
    text-decoration: none;
}

.nav-tab.active {
    color: #1b68ff;
    background-color: #fff;
    border-bottom-color: #1b68ff;
    font-weight: 600;
}

.nav-tab .badge {
    margin-left: 8px;
    font-size: 11px;
    padding: 2px 6px;
    border-radius: 10px;
}

/* 状态标签样式 */
.badge {
    display: inline-block;
    padding: 0.25em 0.6em;
    font-size: 75%;
    font-weight: 500;
    line-height: 1;
    text-align: center;
    white-space: nowrap;
    vertical-align: baseline;
    border-radius: 0.25rem;
}

.badge-success {
    color: #fff;
    background-color: #28a745;
}

.badge-secondary {
    color: #fff;
    background-color: #6c757d;
}

.badge-danger {
    color: #fff;
    background-color: #dc3545;
}

.badge-warning {
    color: #212529;
    background-color: #ffc107;
}

.badge-info {
    color: #fff;
    background-color: #17a2b8;
}

.badge-light {
    color: #495057;
    background-color: #f8f9fa;
    border: 1px solid #dee2e6;
}

/* 筛选表单样式 */
.filter-form {
    display: flex;
    flex-wrap: wrap;
    align-items: center;
    gap: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 6px;
    margin-bottom: 20px;
}

.filter-form-item {
    display: flex;
    align-items: center;
    gap: 8px;
}

.filter-form-item label {
    font-weight: 600;
    color: #333;
    white-space: nowrap;
    margin: 0;
    font-size: 14px;
}

.filter-form-item .form-control {
    min-width: 120px;
    height: 32px;
    padding: 4px 8px;
    font-size: 14px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.filter-form-item .form-control:focus {
    border-color: #1b68ff;
    box-shadow: 0 0 0 0.2rem rgba(27, 104, 255, 0.25);
}

/* 按钮样式 */
.btn {
    display: inline-block;
    padding: 6px 12px;
    font-size: 14px;
    font-weight: 500;
    line-height: 1.5;
    text-align: center;
    text-decoration: none;
    vertical-align: middle;
    cursor: pointer;
    border: 1px solid transparent;
    border-radius: 4px;
    transition: all 0.15s ease-in-out;
}

.btn-light-primary {
    background-color: #e6f0ff;
    color: #1b68ff;
    border: 1px solid #cce0ff;
}

.btn-light-primary:hover {
    background-color: #d1e3ff;
    color: #0056b3;
    text-decoration: none;
}

.btn-light-secondary {
    background-color: #f0f0f0;
    color: #666666;
    border: 1px solid #dddddd;
}

.btn-light-secondary:hover {
    background-color: #e0e0e0;
    color: #444444;
    text-decoration: none;
}

/* 移除链接下划线 */
a, a:hover, a:focus, a:active {
    text-decoration: none !important;
}

/* 响应式调整 */
@media (max-width: 768px) {
    .nav-tabs {
        flex-direction: column;
    }

    .nav-tab {
        border-bottom: 1px solid #e9ecef;
        border-radius: 0;
    }

    .nav-tab.active {
        border-bottom-color: #e9ecef;
        border-left: 3px solid #1b68ff;
    }

    .filter-form {
        flex-direction: column;
        align-items: stretch;
        gap: 10px;
    }

    .filter-form-item {
        flex-direction: column;
        align-items: stretch;
        gap: 5px;
    }

    .filter-form-item label {
        text-align: left;
    }
}
</style>

<!-- 消息提示 -->
<?php if($message): ?>
<div class="alert alert-success"><?php echo $message ?? ""; ?></div>
<?php endif; ?>

<!-- 举报管理 -->
<div class="card mb-4">
    <div class="card-header d-flex justify-content-between align-items-center">
        <div>
            <h3 class="card-title">举报列表</h3>
        </div>
    </div>
    
    <div class="card-body">
        <!-- 状态选项卡 -->
        <div class="nav-tabs">
            <a href="report.php?status=0" class="nav-tab <?php if(!null !== ($status ?? null) || $status == 0): ?>active<?php endif; ?>">
                <i class="fas fa-exclamation-circle"></i>
                <span>未处理</span>
                <?php if(null !== ($status_counts ?? null) && is_array($status_counts) && array_key_exists('pending', $status_counts) && $status_counts['pending'] > 0): ?>
                <span class="badge badge-danger"><?php echo (isset($status_counts['pending'])) ? $status_counts['pending'] : ""; ?></span>
                <?php endif; ?>
            </a>
            <a href="report.php?status=1" class="nav-tab <?php if(null !== ($status ?? null) && $status == 1): ?>active<?php endif; ?>">
                <i class="fas fa-check-circle"></i>
                <span>已处理</span>
            </a>
            <a href="report.php?status=-1" class="nav-tab <?php if(null !== ($status ?? null) && $status == -1): ?>active<?php endif; ?>">
                <i class="fas fa-list"></i>
                <span>全部举报</span>
            </a>
        </div>

        <!-- 高级筛选表单 -->
        <form action="report.php" method="get">
            <input type="hidden" name="status" value="<?php if(null !== ($status ?? null)): ?><?php echo $status ?? ""; ?><?php else: ?>0<?php endif; ?>">
            <div class="filter-form">
                <div class="filter-form-item">
                    <label>举报类型:</label>
                    <select name="type" class="form-control" style="min-width: 150px;">
                        <option value="">全部类型</option>
                        <option value="虚假信息" <?php if(null !== ($type ?? null) && $type == '虚假信息'): ?>selected<?php endif; ?>>虚假信息</option>
                        <option value="诈骗信息" <?php if(null !== ($type ?? null) && $type == '诈骗信息'): ?>selected<?php endif; ?>>诈骗信息</option>
                        <option value="违法信息" <?php if(null !== ($type ?? null) && $type == '违法信息'): ?>selected<?php endif; ?>>违法信息</option>
                        <option value="广告信息" <?php if(null !== ($type ?? null) && $type == '广告信息'): ?>selected<?php endif; ?>>广告信息</option>
                        <option value="违规信息" <?php if(null !== ($type ?? null) && $type == '违规信息'): ?>selected<?php endif; ?>>违规信息</option>
                        <option value="其他问题" <?php if(null !== ($type ?? null) && $type == '其他问题'): ?>selected<?php endif; ?>>其他问题</option>
                    </select>
                </div>

                <div class="filter-form-item">
                    <label>信息ID:</label>
                    <input type="number" name="post_id" value="<?php if(null !== ($post_id ?? null)): ?><?php echo $post_id ?? ""; ?><?php endif; ?>" placeholder="输入ID..." class="form-control" style="width: 100px;">
                </div>

                <div class="filter-form-item">
                    <label>关键词:</label>
                    <input type="text" name="keyword" value="<?php if(null !== ($keyword ?? null)): ?><?php echo $keyword ?? ""; ?><?php endif; ?>" placeholder="搜索举报内容..." class="form-control" style="min-width: 200px;">
                </div>

                <div class="filter-form-item">
                    <button type="submit" class="btn btn-light-primary">
                        <i class="fas fa-search"></i> 筛选
                    </button>
                    <a href="report.php?status=<?php if(null !== ($status ?? null)): ?><?php echo $status ?? ""; ?><?php else: ?>0<?php endif; ?>" class="btn btn-light-secondary">
                        <i class="fas fa-undo"></i> 重置
                    </a>
                </div>
            </div>
        </form>
        
        <!-- 举报列表 -->
        <form id="reportForm" action="report.php?action=batch_delete" method="post">
            <div class="table-responsive">
                <table class="table table-vcenter table-bordered table-hover">
                    <thead>
                        <tr>
                            <th class="col-checkbox"><input type="checkbox" id="selectAll"></th>
                            <th class="col-id">ID</th>
                            <th class="col-post-id">信息ID</th>
                            <th class="col-post-title">信息标题</th>
                            <th class="col-type">举报类型</th>
                            <th class="col-content">举报内容</th>
                            <th class="col-contact">联系方式</th>
                            <th class="col-time">提交时间</th>
                            <th class="col-status">状态</th>
                            <th class="col-actions" style="text-align: right;">操作</th>
                        </tr>
                    </thead>
                    <tbody>
                        <?php if(!$reports): ?>
                        <tr>
                            <td colspan="10" class="text-center">暂无举报数据</td>
                        </tr>
                        <?php else: ?>
                        <?php if(null !== ($reports ?? null) && is_array($reports)): foreach($reports as $report): ?>
                        <tr>
                            <td>
                                <input type="checkbox" name="report_ids[]" class="report-checkbox" value="<?php echo (isset($report['id'])) ? $report['id'] : ""; ?>">
                            </td>
                            <td><?php echo (isset($report['id'])) ? $report['id'] : ""; ?></td>
                            <td><?php echo (isset($report['post_id'])) ? $report['post_id'] : ""; ?></td>
                            <td title="<?php echo (isset($report['post_title'])) ? $report['post_title'] : ""; ?>" style="font-weight: 500;">
                                <?php if($report['post_id'] > 0 && $report['category_pinyin'] != ''): ?>
                                <a href="../<?php echo (isset($report['category_pinyin'])) ? $report['category_pinyin'] : ""; ?>/<?php echo (isset($report['post_id'])) ? $report['post_id'] : ""; ?>.html" target="_blank" style="color: #333; text-decoration: none; overflow: hidden; text-overflow: ellipsis; display: block;">
                                    <?php echo (isset($report['post_title'])) ? $report['post_title'] : ""; ?>
                                </a>
                                <?php else: ?>
                                <span style="color: #999;"><?php echo (isset($report['post_title'])) ? $report['post_title'] : ""; ?></span>
                                <?php endif; ?>
                            </td>
                            <td>
                                <?php echo (isset($report['type'])) ? $report['type'] : ""; ?>
                            </td>
                            <td title="<?php echo (isset($report['content'])) ? $report['content'] : ""; ?>">
                                <div style="max-width: 250px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis;">
                                    <?php echo (isset($report['content'])) ? $report['content'] : ""; ?>
                                </div>
                            </td>
                            <td><?php echo null !== ((null !== ($report ?? null)) ? ($report['tel']) : null) && ((null !== ($report ?? null)) ? ($report['tel']) : null) !== "" ? (null !== ($report ?? null)) ? ($report['tel']) : null : '--'; ?></td>
                            <td><?php echo (isset($report['created_at'])) ? $report['created_at'] : ""; ?></td>
                            <td>
                                <a href="report.php?action=toggle_status&id=<?php echo (isset($report['id'])) ? $report['id'] : ""; ?>" class="status-toggle" title="点击切换状态">
                                    <?php if($report['status'] == 0): ?>
                                        <span class="badge badge-warning">未处理</span>
                                    <?php else: ?>
                                        <span class="badge badge-success">已处理</span>
                                    <?php endif; ?>
                                </a>
                            </td>
                            <td>
                                <div class="info-actions">
                                    <a href="report.php?action=detail&id=<?php echo (isset($report['id'])) ? $report['id'] : ""; ?>" class="btn btn-sm btn-light-primary">详情</a>
                                    <?php if($report['post_id'] > 0): ?>
                                    <a href="info.php?action=edit&id=<?php echo (isset($report['post_id'])) ? $report['post_id'] : ""; ?>" target="_blank" class="btn btn-sm btn-light-info">查看信息</a>
                                    <?php endif; ?>
                                    <a href="report.php?action=delete&id=<?php echo (isset($report['id'])) ? $report['id'] : ""; ?>" class="btn btn-sm btn-light-danger" onclick="return confirm('确定要删除这条举报记录吗？')">删除</a>
                                </div>
                            </td>
                        </tr>
                        <?php endforeach; endif; ?>
                        <?php endif; ?>
                    </tbody>
                </table>
            </div>
            
            <!-- 分页 -->
            <div style="margin-top: 20px; display: flex; justify-content: space-between; align-items: center; flex-wrap: wrap;">
                <!-- 左侧全选和批量删除 -->
                <div style="margin-bottom: 15px;">
                    <button type="submit" id="batchDeleteBtn" class="btn btn-sm btn-light-danger" style="height: 32px; line-height: 1; padding: 0 12px;">批量删除</button>
                </div>
                
                <!-- 分页 -->
                <div style="flex: 1; text-align: right;">
                    <?php if($pagination['total_pages'] > 1): ?>
                    <div>
                        <div class="simple-pagination" style="justify-content: flex-end;">
                            <?php if($pagination['current_page'] > 1): ?>
                            <a href="<?php echo (isset($pagination['previous_link'])) ? $pagination['previous_link'] : ""; ?>" class="pagination-btn">上一页</a>
                            <?php else: ?>
                            <span class="pagination-btn disabled">上一页</span>
                            <?php endif; ?>
                            
                            <?php if(null !== ($pagination ?? null) && is_array($pagination['page_links'])): foreach($pagination['page_links'] as $page => $link): ?>
                            <a href="<?php echo $link ?? ""; ?>" class="pagination-btn <?php if($page == $pagination['current_page']): ?>active<?php endif; ?>"><?php echo $page ?? ""; ?></a>
                            <?php endforeach; endif; ?>
                            
                            <?php if($pagination['current_page'] < $pagination['total_pages']): ?>
                            <a href="<?php echo (isset($pagination['next_link'])) ? $pagination['next_link'] : ""; ?>" class="pagination-btn">下一页</a>
                            <?php else: ?>
                            <span class="pagination-btn disabled">下一页</span>
                            <?php endif; ?>
                            
                            <span style="margin-left: 10px; color: #6c757d; font-size: 14px;">共 <?php echo (isset($pagination['total_pages'])) ? $pagination['total_pages'] : ""; ?> 页</span>
                        </div>
                    </div>
                    <?php endif; ?>
                </div>
            </div>
        </form>
    </div>
</div>

        </div>
        <!-- 主内容区 (结束) -->
    </div>
    <!-- wrapper (结束) -->
</body>
</html>  

<script>
document.addEventListener('DOMContentLoaded', function() {
    // 全选/取消全选
    var selectAll = document.getElementById('selectAll');
    var checkboxes = document.getElementsByClassName('report-checkbox');
    
    // 顶部全选按钮事件
    selectAll.addEventListener('change', function() {
        for (var i = 0; i < checkboxes.length; i++) {
            checkboxes[i].checked = selectAll.checked;
        }
    });
    
    // 当单个checkbox改变时，检查是否需要更新全选框状态
    for (var i = 0; i < checkboxes.length; i++) {
        checkboxes[i].addEventListener('change', function() {
            var allChecked = true;
            for (var j = 0; j < checkboxes.length; j++) {
                if (!checkboxes[j].checked) {
                    allChecked = false;
                    break;
                }
            }
            // 同步顶部全选按钮
            selectAll.checked = allChecked;
        });
    }
    
    // 批量删除提交前确认
    var reportForm = document.getElementById('reportForm');
    if (reportForm) {
        reportForm.addEventListener('submit', function(e) {
            var checkedBoxes = document.querySelectorAll('input[name="report_ids[]"]:checked');
            if (checkedBoxes.length === 0) {
                e.preventDefault();
                alert('请至少选择一条记录进行删除');
                return false;
            }
            
            if (!confirm('确定要删除选中的 ' + checkedBoxes.length + ' 条举报记录吗？')) {
                e.preventDefault();
                return false;
            }
            
            return true;
        });
    }
});
</script> 