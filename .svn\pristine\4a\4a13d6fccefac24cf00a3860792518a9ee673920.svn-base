{if !empty($posts)}
    {foreach $posts as $post}
    <?php
    // 计算有效期
    if (!empty($post['expired_at'])) {
        $days = getRemainingDaysInt($post['expired_at']);
        $validity = $days > 0 ? $days.'天' : '已过期';
    } else {
        $validity = '长期';
    }
    ?>
    <!-- 文本视图项目 -->
    <div class="list-item {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}is-top{/if}">
        <div class="item-title">
            <?php if ($validity == '已过期'): ?>
            <a href="/{$category.pinyin}/{$post.id}.html" class="expired">{$post.title}</a>
            <?php else: ?>
            <a href="/{$category.pinyin}/{$post.id}.html">{$post.title}</a>
            <?php endif; ?>
            {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}
            <span class="top-tag">顶</span>
            {/if}
        </div>
        <div class="item-meta">
            <span class="meta-item">
                <i class="fas fa-map-marker-alt"></i>
                {$post.region_name|default:'未知地区'}
            </span>
            <span class="meta-item">
                <i class="fas fa-calendar-alt"></i>
                <?php echo $validity; ?>
            </span>
            <span class="meta-item">
                <i class="far fa-clock"></i>
                {$post.updated_at|friendlyTime}
            </span>
        </div>
    </div>

    <!-- 图片视图项目 -->
    <div class="post-item {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}is-top{/if}">
        <div class="post-image">
            {if !empty($post.image_url)}
            <img src="{$post.image_url}" alt="{$post.title}">
            {else}
            <img src="/static/images/no-image.png" alt="无图片">
            {/if}
        </div>
        <div class="post-content">
            <div class="post-title">
                {if !empty($post.is_top_category) || !empty($post.is_top_subcategory)}<span class="top-dot"></span>{/if}
                <?php
                $days = getRemainingDaysInt($post['expired_at']);
                if (!empty($post['expired_at']) && $days <= 0):
                ?>
                <a href="/{$category.pinyin}/{$post.id}.html" class="post-expired">{$post.title}</a>
                <?php else: ?>
                <a href="/{$category.pinyin}/{$post.id}.html">{$post.title}</a>
                <?php endif; ?>
            </div>
            <div class="post-meta">
                {$post.updated_at|friendlyTime}
                <?php
                if (!empty($post['expired_at'])) {
                    echo ' · ';
                    $days = getRemainingDaysInt($post['expired_at']);
                    echo $days > 0 ? $days.'天' : '已过期';
                }
                ?>
            </div>
        </div>
    </div>
    {/foreach}
{/if}