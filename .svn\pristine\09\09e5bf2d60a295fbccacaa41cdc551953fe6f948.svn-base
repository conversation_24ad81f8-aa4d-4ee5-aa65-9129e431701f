{include file="header.htm"}

<div class="container">
    <div class="row">
        <div class="col-md-9">
            <div class="category-header">
                <h1 class="category-title">{$category.catname}</h1>
                {if !empty($category.description)}
                <div class="category-description">{$category.description}</div>
                {/if}
            </div>

            <!-- 信息列表 -->
            <div class="info-section">
                {if !empty($posts)}
                <!-- 普通信息 -->
                <div class="info-list">
                    {foreach $posts as $post}
                    {assign var="guoqi" value="长期"}
                    
                    {if !empty($post.image_url)}
                    <!-- 普通图文列表项 -->
                    <li class="image-list-item">
                        <div class="item-image">
                            <a href="/{$post.category_pinyin}/{$post.id}.html" target="_blank">
                                <img src="{$post.image_url}" alt="{$post.title}" loading="lazy">
                            </a>
                        </div>
                        <div class="item-content">
                            <div class="item-title">
                                <a target="_blank" href="/{$post.category_pinyin}/{$post.id}.html">{$post.title}</a>
                                {if isset($post.is_top_category) && $post.is_top_category}<span class="top-tag">顶</span>{/if}
                            </div>
                            <div class="item-summary">
                                {if !empty($post.summary)}
                                    {$post.summary|truncate:150:"..."}
                                {else}
                                    查看详情...
                                {/if}
                            </div>
                            <div class="item-meta">
                                <span class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    {$post.region_name|default:"未知地区"}
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-calendar-alt"></i>
                                    {$guoqi}
                                </span>
                                <span class="meta-item">
                                    <i class="far fa-clock"></i>
                                    {$post.updated_at|friendlyTime}
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-eye"></i>
                                    {$post.view_count|default:0}次浏览
                                </span>
                            </div>
                        </div>
                    </li>
                    {else}
                    <!-- 普通纯文本列表项 -->
                    {php}
                        // 检查是否显示置顶标记和选择图标
                        $current_time = time();
                        $show_top_tag = false;
                        $icon_class = 'icon_title1.gif'; // 默认普通图标

                        // 检查大分类置顶
                        if (isset($post['is_top_category']) && $post['is_top_category'] == 1) {
                            if (!isset($post['top_category_expire']) || $post['top_category_expire'] == 0 || $post['top_category_expire'] > $current_time) {
                                $show_top_tag = true;
                                $icon_class = 'icon_title2.gif'; // 置顶图标
                            }
                        }

                        // 检查小分类置顶
                        if (!$show_top_tag && isset($post['is_top_subcategory']) && $post['is_top_subcategory'] == 1) {
                            if (!isset($post['top_subcategory_expire']) || $post['top_subcategory_expire'] == 0 || $post['top_subcategory_expire'] > $current_time) {
                                $show_top_tag = true;
                                $icon_class = 'icon_title2.gif'; // 置顶图标
                            }
                        }

                        // 输出li标签和图标，为置顶信息添加CSS类
                        $li_class = $show_top_tag ? 'text-list-item top-item' : 'text-list-item';
                        echo '<li class="' . $li_class . '" style="background:url(/template/pc/images/' . $icon_class . ') left no-repeat; padding-left: 15px;">';
                    {/php}
                        <div class="info-title">
                            {if $post.category_name}<a href="/{$post.category_pinyin}/" style="color: #999; text-decoration: none; margin-right: 5px;">[{$post.category_name}]</a>{/if}
                            <a target="_blank" href="/{$post.category_pinyin}/{$post.id}.html" {php}if ($show_top_tag) echo 'style="color: #EE3131;"';{/php}>{$post.title}</a>
                            {php}if ($show_top_tag) echo '<span class="top-tag">顶</span>';{/php}
                        </div>
                        <div class="info-meta">
                            <span class="area">{$post.region_name|default:"未知地区"}</span>
                            <span class="validity">{$guoqi}</span>
                            <span class="time">{$post.updated_at|friendlyTime}</span>
                        </div>
                    {php}echo '</li>';{/php}
                    {/if}
                    {/foreach}
                </div>
                {else}
                <div class="no-data">
                    <p>暂无信息</p>
                </div>
                {/if}
            </div>

            <!-- 分页 -->
            {if !empty($pagination)}
            <div class="pagination-wrapper">
                {$pagination}
            </div>
            {/if}
        </div>

        <div class="col-md-3">
            {include file="common/sidebar.htm"}
        </div>
    </div>
</div>

{include file="footer.htm"}
