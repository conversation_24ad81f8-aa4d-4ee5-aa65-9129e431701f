<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1753251380,
  'data' => 
  array (
    'posts' => 
    array (
      0 => 
      array (
        'id' => '104037',
        'user_id' => '0',
        'category_id' => '6',
        'region_id' => '80',
        'expire_days' => '7',
        'title' => '低价车辆出售93元',
        'is_top_home' => '0',
        'is_top_category' => '0',
        'is_top_subcategory' => '0',
        'top_home_expire' => NULL,
        'top_category_expire' => NULL,
        'top_subcategory_expire' => NULL,
        'view_count' => '1',
        'image_count' => '1',
        'status' => '1',
        'created_at' => '1746493729',
        'updated_at' => '1746493729',
        'expired_at' => '1747098529',
        'is_expired' => '0',
        'region_name' => '德州市',
        'summary' => '保证正品，假一赔十，支持验货后付款，让您买得放心。

物品九成新，几乎没有使用痕迹，包装齐全，说明书配件都有。

保证正品，假一赔十，支持验货后付款，让您买得放心。...',
        'image_url' => '/uploads/thumbs/202505/68196121e9d22.jpg',
        'category_pinyin' => 'hezu',
        'url' => 'http://www.fenlei.com/hezu/104037.html',
        'detail_url' => 'http://www.fenlei.com/hezu/104037.html',
      ),
    ),
    'totalPosts' => 1,
  ),
);
