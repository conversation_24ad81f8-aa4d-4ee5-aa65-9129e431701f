<!DOCTYPE html>
<?php
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
?>
<html lang="zh-CN" class="<?php echo $theme_class; ?>">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title><?php 
        if (empty($keyword)) {
            echo '信息搜索';
        } else {
            echo '搜索：' . $keyword;
        }
        echo ' - ' . $site_name; 
    ?></title>
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css">
    <link rel="stylesheet" href="/template/m/css/common.css">

    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    <style>
        /* 搜索页面特有的样式 - 扁平化通栏设计 */
        .search-page-form {
            background-color: #fff;
            padding: 15px 10px;
            margin-bottom: 0;
            box-shadow: none;
            border-radius: 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .search-page-box {
            display: flex;
            width: 100%;
        }
        .search-page-input {
            flex: 1;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 0;
            font-size: 14px;
            outline: none;
            height: 40px;
            box-sizing: border-box;
        }
        .search-page-input:focus {
            border-color: var(--primary-color);
        }
        .search-page-btn {
            width: 60px;
            height: 40px;
            background-color: var(--primary-color);
            color: #fff;
            border: none;
            border-radius: 0;
            cursor: pointer;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            transition: all 0.3s ease;
        }
        .search-page-btn:disabled {
            background-color: #ccc;
            cursor: not-allowed;
        }
        .search-page-btn .btn-loading {
            display: none;
        }
        .search-page-btn.loading .btn-icon {
            display: none;
        }
        .search-page-btn.loading .btn-loading {
            display: inline-block;
        }

        /* 搜索加载遮罩 */
        .search-loading-overlay {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0, 0, 0, 0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 9999;
        }

        .loading-content {
            background: white;
            padding: 20px;
            border-radius: 8px;
            text-align: center;
            color: var(--primary-color);
            font-size: 16px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .loading-content i {
            font-size: 24px;
            margin-bottom: 10px;
            display: block;
        }
        .search-popular {
            margin-top: 15px;
            padding-top: 10px;
            border-top: 1px solid #f0f0f0;
            font-size: 13px;
            color: #666;
            display: flex;
            flex-wrap: wrap;
            align-items: center;
        }
        .search-popular span {
            margin-right: 5px;
            margin-bottom: 5px;
        }
        .search-popular-tag {
            display: inline-block;
            background-color: #f5f5f5;
            color: #666;
            padding: 6px 10px;
            margin: 0 8px 8px 0;
            border-radius: 0;
            font-size: 12px;
            text-decoration: none;
            line-height: 1.2;
        }
        .search-popular-tag:hover, .search-popular-tag:active {
            background-color: var(--primary-color);
            color: #fff;
        }
        
        .result-count {
            padding: 10px;
            color: #666;
            font-size: 13px;
            background-color: #fff;
            margin-bottom: 0;
            border-radius: 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .result-item {
            background-color: #fff;
            padding: 15px 10px;
            margin-bottom: 0;
            box-shadow: none;
            border-radius: 0;
            border-bottom: 1px solid #f0f0f0;
        }
        /* 搜索结果列表样式 */
        .result-list {
            background-color: #fff;
        }

        .result-item {
            padding: 15px 10px;
            border-bottom: 1px solid #f0f0f0;
        }

        .result-item:last-child {
            border-bottom: none;
        }

        .item-title {
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }

        .item-title a {
            color: #333;
            text-decoration: none;
            font-size: 15px;
            font-weight: 500;
            line-height: 1.4;
            word-break: break-word;
            flex: 1;
        }

        .item-title a.expired {
            color: #999;
            text-decoration: line-through;
        }

        .top-tag {
            background: var(--primary-color);
            color: white;
            font-size: 10px;
            padding: 2px 4px;
            border-radius: 2px;
            margin-left: 8px;
            flex-shrink: 0;
        }

        .item-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            color: #999;
            font-size: 12px;
        }

        .meta-item {
            display: flex;
            align-items: center;
            white-space: nowrap;
        }

        .meta-item i {
            margin-right: 4px;
            font-size: 11px;
            width: 12px;
            text-align: center;
        }

        .meta-item:first-child i {
            color: #ff6b6b;
        }

        .meta-item:nth-child(2) i {
            color: #4ecdc4;
        }

        .meta-item:last-child i {
            color: #45b7d1;
        }
        .highlight {
            background-color: #fff3cd;
            color: #856404;
            font-weight: bold;
            padding: 2px 4px;
            border-radius: 2px;
        }
        .no-results {
            background-color: #fff;
            padding: 20px 10px;
            text-align: center;
            margin-bottom: 0;
            border-radius: 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .no-results p {
            margin-bottom: 10px;
        }
        .no-results ul {
            text-align: left;
            padding-left: 20px;
            margin-top: 10px;
        }
        .no-results li {
            margin-bottom: 5px;
            line-height: 1.5;
        }
        
        /* 修复布局问题 */
        .container {
            padding: 0;
            max-width: 100%;
            box-sizing: border-box;
        }
        
        /* 修复面包屑和导航样式 */
        .breadcrumb {
            padding: 10px;
            background-color: #fff;
            margin-bottom: 0;
            border-bottom: 1px solid #f0f0f0;
            font-size: 13px;
            overflow-x: auto;
            white-space: nowrap;
            display: flex;
            align-items: center;
        }
        
        .breadcrumb .container {
            display: flex;
            align-items: center;
            padding: 0;
            width: auto;
        }
        
        .breadcrumb a {
            color: #666;
            text-decoration: none;
        }
        
        .breadcrumb .separator {
            margin: 0 8px;
            color: #ccc;
        }
        
        .breadcrumb .separator:after {
            content: '>';
            font-size: 12px;
        }
        
        .breadcrumb .current {
            color: #333;
            max-width: 150px;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }
        
        /* 分页样式扁平化 */
        .pagination {
            display: flex;
            justify-content: center;
            padding: 15px 0;
            background-color: #fff;
            margin-bottom: 0;
            border-bottom: 1px solid #f0f0f0;
        }
        .page-item {
            margin: 0 3px;
        }
        .page-link {
            display: inline-block;
            padding: 6px 12px;
            background-color: #f5f5f5;
            border: none;
            color: #333;
            text-decoration: none;
            border-radius: 0;
            font-size: 13px;
        }
        .page-item.active .page-link {
            background-color: var(--primary-color);
            color: #fff;
        }
        
        /* 确保标题居中 */
        .header-title {
            text-align: center;
            font-size: 16px;
            font-weight: normal;
            display: block;
            width: 100%;
            position: absolute;
            left: 0;
            right: 0;
        }
        
        /* 响应式调整 */
        @media screen and (max-width: 360px) {
            .search-page-btn {
                width: 50px;
                font-size: 12px;
            }
            .search-popular span {
                display: block;
                width: 100%;
                margin-bottom: 5px;
            }
        }
        
        /* 修复搜索表单在小屏幕上的样式 */
        @media screen and (max-width: 320px) {
            .search-page-form {
                padding: 12px 10px 15px;
            }
            .search-page-input {
                height: 38px;
                padding: 8px;
                font-size: 13px;
            }
            .search-page-btn {
                width: 45px;
                height: 38px;
            }
            .search-popular {
                margin-top: 12px;
                padding-top: 8px;
            }
            .search-popular-tag {
                padding: 5px 8px;
                margin: 0 6px 6px 0;
                font-size: 11px;
            }
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">信息搜索</div>
        </div>
    </header>
    
    <div class="breadcrumb">
        <div class="container">
            <a href="/">首页</a>
            <span class="separator"></span>
            <span>信息搜索</span>
            <?php if (!empty($keyword)): ?>
            <span class="separator"></span>
            <span class="current"><?php echo $keyword; ?></span>
            <?php endif; ?>
        </div>
    </div>
    
    <div class="container">
        <div class="search-page-form">
            <!-- 搜索错误提示 -->
            <?php if (!empty($search_error)): ?>
            <div class="search-error-message" style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; margin-bottom: 10px; border-radius: 4px; font-size: 14px;">
                <i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>
                <span id="search-error-text"><?php echo $search_error; ?></span>
                <span id="countdown-timer" style="font-weight: bold;"></span>
            </div>
            <?php endif; ?>

            <form action="/search.php" method="get" id="search-form">
                <div class="search-page-box">
                    <input type="text" name="keyword" value="<?php echo isset($keyword) ? $keyword : ''; ?>" class="search-page-input" placeholder="请输入要搜索的关键词" required id="search-input">
                    <button type="submit" class="search-page-btn" id="search-btn">
                        <span class="btn-icon"><i class="fas fa-search"></i></span>
                        <span class="btn-loading" style="display: none;"><i class="fas fa-spinner fa-spin"></i></span>
                    </button>
                </div>
                <!-- 搜索加载提示 -->
                <div class="search-loading-overlay" id="search-loading" style="display: none;">
                    <div class="loading-content">
                        <i class="fas fa-spinner fa-spin"></i>
                        <span>正在搜索，请稍候...</span>
                    </div>
                </div>
                <div class="search-popular">
                    <span>热门搜索：</span>
                    <a href="/search.php?keyword=招聘" class="search-popular-tag">招聘</a>
                    <a href="/search.php?keyword=兼职" class="search-popular-tag">兼职</a>
                    <a href="/search.php?keyword=房屋出租" class="search-popular-tag">房屋出租</a>
                    <a href="/search.php?keyword=二手车" class="search-popular-tag">二手车</a>
                    <a href="/search.php?keyword=手机" class="search-popular-tag">手机</a>
                </div>
            </form>
        </div>
        
        <?php if (!empty($keyword)): ?>
            <?php if (!empty($search_results)): ?>
                <div class="result-count"><i class="fas fa-list"></i> 共找到 <strong><?php echo $total_count; ?></strong> 条相关信息</div>
                
                <div class="result-list">
                    <?php foreach ($search_results as $result): ?>
                        <?php
                        // 计算有效期
                        if (!empty($result['expired_at'])) {
                            $days = getRemainingDaysInt($result['expired_at']);
                            $validity = $days > 0 ? $days.'天' : '已过期';
                        } else {
                            $validity = '长期';
                        }
                        ?>
                        <div class="result-item">
                            <div class="item-title">
                                <a href="/<?php echo $result['category_pinyin']; ?>/<?php echo $result['id']; ?>.html"
                                   <?php if ($validity == '已过期') echo 'class="expired"'; ?>>
                                    <?php echo $result['title_highlight']; ?>
                                </a>
                                <?php if (isset($result['is_top_category']) && $result['is_top_category']): ?>
                                    <span class="top-tag">顶</span>
                                <?php endif; ?>
                            </div>
                            <div class="item-meta">
                                <span class="meta-item">
                                    <i class="fas fa-map-marker-alt"></i>
                                    <?php echo $result['region_name']; ?>
                                </span>
                                <span class="meta-item">
                                    <i class="fas fa-calendar-alt"></i>
                                    <?php echo $validity; ?>
                                </span>
                                <span class="meta-item">
                                    <i class="far fa-clock"></i>
                                    <?php echo friendlyTime($result['created_at']); ?>
                                </span>
                            </div>
                        </div>
                    <?php endforeach; ?>
                </div>
                
                <?php if ($total_pages > 1): ?>
                    <div class="pagination">
                        <?php if ($current_page > 1): ?>
                            <div class="page-item">
                                <a href="/search.php?keyword=<?php echo urlencode($keyword); ?>&page=<?php echo $current_page - 1; ?>" class="page-link"><i class="fas fa-chevron-left"></i> 上一页</a>
                            </div>
                        <?php endif; ?>
                        
                        <?php
                        // 显示3个分页按钮（移动端空间有限）
                        $startPage = max(1, $current_page - 1);
                        $endPage = min($total_pages, $startPage + 2);
                        if ($endPage - $startPage < 2) {
                            $startPage = max(1, $endPage - 2);
                        }
                        
                        for ($i = $startPage; $i <= $endPage; $i++):
                        ?>
                            <div class="page-item <?php echo $i == $current_page ? 'active' : ''; ?>">
                                <a href="/search.php?keyword=<?php echo urlencode($keyword); ?>&page=<?php echo $i; ?>" class="page-link"><?php echo $i; ?></a>
                            </div>
                        <?php endfor; ?>
                        
                        <?php if ($current_page < $total_pages): ?>
                            <div class="page-item">
                                <a href="/search.php?keyword=<?php echo urlencode($keyword); ?>&page=<?php echo $current_page + 1; ?>" class="page-link">下一页 <i class="fas fa-chevron-right"></i></a>
                            </div>
                        <?php endif; ?>
                    </div>
                <?php endif; ?>
                
            <?php else: ?>
                <div class="no-results">
                    <p><i class="fas fa-exclamation-circle"></i> 没有找到与 <strong>"<?php echo $keyword; ?>"</strong> 相关的信息</p>
                    <p>建议：</p>
                    <ul>
                        <li>请检查您的拼写</li>
                        <li>尝试使用更常见的关键词</li>
                        <li>尝试使用更短的关键词</li>
                    </ul>
                </div>
            <?php endif; ?>
        <?php else: ?>
            <div class="no-results">
                <p><i class="fas fa-info-circle"></i> 请在上方搜索框中输入关键词进行搜索</p>
            </div>
        <?php endif; ?>
    </div>
    
    
    <!-- 底部版权 -->
    <footer>
    <div class="container">
        <div class="footer-nav">
            <a href="/login.php">登录/注册</a>
            <a href="/fee.php">电话费用</a>
            <a href="/feedback.php">用户反馈</a>
        </div>
        <div class="theme-switcher">
            <p>主题切换</p>
            <div class="theme-dots">
                <a href="javascript:void(0);" onclick="switchTheme('red')" class="theme-dot theme-red" title="红色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('blue')" class="theme-dot theme-blue" title="蓝色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('green')" class="theme-dot theme-green" title="绿色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('purple')" class="theme-dot theme-purple" title="紫色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('orange')" class="theme-dot theme-orange" title="橙色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('pink')" class="theme-dot theme-pink" title="粉色主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('ocean')" class="theme-dot theme-ocean" title="海洋主题"></a>
                <a href="javascript:void(0);" onclick="switchTheme('wechat')" class="theme-dot theme-wechat" title="微信风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('alipay')" class="theme-dot theme-alipay" title="支付宝风格"></a>
                <a href="javascript:void(0);" onclick="switchTheme('simple')" class="theme-dot theme-simple" title="简约主题"></a>
            </div>
        </div>
        <div class="footer-info">
            <p>京ICP证060405号</p>
            <p>客户服务热线: 10105858</p>
        </div>
    </div>
    <script>
    // 主题切换功能
    function switchTheme(theme) {
        // 设置cookie，有效期30天
        var date = new Date();
        date.setTime(date.getTime() + (30 * 24 * 60 * 60 * 1000));
        document.cookie = "site_theme=" + theme + "; expires=" + date.toUTCString() + "; path=/";
        
        // 更新页面上的主题类
        document.documentElement.className = document.documentElement.className.replace(/theme-\w+/g, '');
        document.documentElement.classList.add('theme-' + theme);
        
        // 同时更新body的主题类，确保背景色只应用于内容区域
        document.body.className = document.body.className.replace(/theme-\w+/g, '');
        document.body.classList.add('theme-' + theme);
        
        // 更新当前选中的主题点
        highlightCurrentTheme(theme);
        
        // 显示切换成功提示
        var themeNames = {
            'red': '红色',
            'blue': '蓝色',
            'green': '绿色',
            'purple': '紫色',
            'orange': '橙色',
            'pink': '粉色',
            'ocean': '海洋',
            'wechat': '微信风格',
            'alipay': '支付宝风格',
            'miui': '小米风格',
            'douyin': '抖音风格',
            'simple': '简约'
        };
        
        // 创建提示元素
        var toast = document.createElement('div');
        toast.className = 'theme-toast';
        toast.textContent = '已切换到' + themeNames[theme] + '主题';
        document.body.appendChild(toast);
        
        // 2秒后移除提示
        setTimeout(function() {
            toast.classList.add('hide');
            setTimeout(function() {
                document.body.removeChild(toast);
            }, 300);
        }, 2000);
    }
    
    // 高亮当前主题
    function highlightCurrentTheme(theme) {
        // 移除所有主题点的高亮
        var themeDots = document.querySelectorAll('.theme-dot');
        themeDots.forEach(function(dot) {
            dot.classList.remove('active');
        });
        
        // 添加当前主题的高亮
        var currentThemeDot = document.querySelector('.theme-dot.theme-' + theme);
        if (currentThemeDot) {
            currentThemeDot.classList.add('active');
        }
    }
    
    // 在页面加载时，根据当前主题高亮对应的主题点
    document.addEventListener('DOMContentLoaded', function() {
        // 获取当前主题
        var currentTheme = 'red'; // 默认主题
        var htmlClass = document.documentElement.className;
        var themeMatch = htmlClass.match(/theme-(\w+)/);
        
        if (themeMatch && themeMatch[1]) {
            currentTheme = themeMatch[1];
            
            // 确保body也具有相同的主题类
            document.body.className = document.body.className.replace(/theme-\w+/g, '');
            document.body.classList.add('theme-' + currentTheme);
        }
        
        // 高亮当前主题
        highlightCurrentTheme(currentTheme);
    });
    </script>
    <style>
    /* 主题切换样式 */
    html, body {
        min-height: 100%;
    }
    
    body {
        background-color: #f5f5f5; /* 默认背景色 */
        margin: 0;
        padding: 0;
    }
    
    /* 确保主题颜色只应用于body */
    html.theme-red, html.theme-blue, html.theme-green, 
    html.theme-purple, html.theme-orange, html.theme-pink,
    html.theme-ocean, html.theme-wechat, html.theme-alipay,
    html.theme-miui, html.theme-douyin {
        background-color: #fff; /* 重置HTML背景为白色 */
    }
    
    /* 主题背景色应用到body */
    body.theme-red { background-color: #fff5f5; }
    body.theme-blue { background-color: #f5f8ff; }
    body.theme-green { background-color: #f5fff8; }
    body.theme-purple { background-color: #f8f5ff; }
    body.theme-orange { background-color: #fff9f5; }
    body.theme-pink { background-color: #fff5f9; }
    body.theme-ocean { background-color: #f5faff; }
    body.theme-wechat { background-color: #f5fff7; }
    body.theme-alipay { background-color: #f5faff; }
    body.theme-simple { background-color: #f8f8f8; }
    
    .theme-switcher {
        text-align: center;
        margin: 15px 0;
        padding: 10px;
        background-color: rgba(255, 255, 255, 0.9);
        border-radius: 10px;
    }
    
    .theme-switcher p {
        margin: 0 0 8px 0;
        font-size: 14px;
        color: #666;
        font-weight: 500;
    }
    
    .theme-dots {
        display: flex;
        flex-wrap: wrap;
        justify-content: center;
        gap: 12px;
        padding: 5px;
    }
    
    .theme-dot {
        display: inline-block;
        width: 22px;
        height: 22px;
        border-radius: 50%;
        cursor: pointer;
        transition: transform 0.2s, box-shadow 0.2s;
        border: 2px solid #fff;
        box-shadow: 0 1px 3px rgba(0,0,0,0.1);
    }
    
    .theme-dot:hover {
        transform: scale(1.2);
        box-shadow: 0 2px 5px rgba(0,0,0,0.2);
    }
    
    .theme-dot.active {
        transform: scale(1.2);
        box-shadow: 0 0 0 2px #fff, 0 0 0 4px var(--primary-color, currentColor);
    }
    
    /* 主题颜色 */
    .theme-red {
        background-color: #e53935;
    }
    
    .theme-blue {
        background-color: #4285f4;
    }
    
    .theme-green {
        background-color: #00a878;
    }
    
    .theme-purple {
        background-color: #7b68ee;
    }
    
    .theme-orange {
        background-color: #ff6b01;
    }
    
    .theme-pink {
        background-color: #e91e63;
    }
    
    .theme-ocean {
        background-color: #006994;
    }
    
    .theme-wechat {
        background-color: #07c160;
    }
    
    .theme-alipay {
        background-color: #1677ff;
    }
    
    .theme-simple {
        background-color: #ffffff;
        border: 1px solid #eeeeee;
    }
    
    /* 主题切换toast提示 */
    .theme-toast {
        position: fixed;
        bottom: 80px;
        left: 50%;
        transform: translateX(-50%);
        background-color: rgba(0, 0, 0, 0.7);
        color: white;
        padding: 10px 20px;
        border-radius: 20px;
        font-size: 14px;
        z-index: 1000;
        opacity: 1;
        transition: opacity 0.3s;
    }
    
    .theme-toast.hide {
        opacity: 0;
    }
    
    /* 简约主题特殊处理 */
    .theme-simple header {
        background-color: #ffffff !important;
        color: #333333 !important;
        border-bottom: 1px solid #eeeeee !important;
        box-shadow: 0 1px 3px rgba(0,0,0,0.05) !important;
    }
    
    .theme-simple .header-back,
    .theme-simple .header-title,
    .theme-simple .header-share,
    .theme-simple .header-search-icon {
        color: #333333 !important;
    }
    
    .theme-simple .header-back:active,
    .theme-simple .header-share:active,
    .theme-simple .header-search-icon:active {
        background-color: rgba(0,0,0,0.05) !important;
    }
    </style>
</footer>

<div style="background-color: #f8f9fa; padding: 15px; border-radius: 5px; border-left: 4px solid #3498db; margin-top: 10px;">
    <h4 style="margin-top: 0; color: #2c3e50;">包含的页脚文件</h4>
    <p>这是一个从m目录下包含的页脚模板文件。演示了include标签的用法。</p>
    <p>当前时间：<?php echo null !== ($current_time ?? null) ? Template::date_format($current_time ?? null, 'Y-m-d H:i:s') : ""; ?></p>
    <p>&copy; <?php echo date("Y"); ?> <?php echo $site_name ?? ""; ?> - 版权所有</p>
</div>
    
    <script>
        // ===== 全局变量 =====
        const SEARCH_HISTORY_KEY = 'search_history';

        // ===== 页面初始化 =====
        // 防止FOUC闪烁
        document.documentElement.classList.add('no-fouc');
        
        // ===== 工具函数 =====
        // 获取URL参数
        function getUrlParam(name) {
            const urlParams = new URLSearchParams(window.location.search);
            return urlParams.get(name);
        }
        
        // ===== 搜索历史功能 =====
        // 获取搜索历史
        function getSearchHistory() {
            try {
                const history = localStorage.getItem(SEARCH_HISTORY_KEY);
                return history ? JSON.parse(history) : [];
            } catch (e) {
                console.error('Failed to get search history:', e);
                return [];
            }
        }
        
        // 添加搜索历史
        function addSearchHistory(keyword) {
            if (!keyword) return;
            
            try {
                let history = getSearchHistory();
                history = history.filter(item => item !== keyword);
                history.unshift(keyword);
                history = history.slice(0, 10);
                localStorage.setItem(SEARCH_HISTORY_KEY, JSON.stringify(history));
            } catch (e) {
                console.error('Failed to add search history:', e);
            }
        }
        
        // ===== 搜索间隔管理 =====
        var SearchInterval = {
            interval: <?php echo isset($config['search_interval']) ? intval($config['search_interval']) : 5; ?>, // 搜索间隔（秒）
            lastSearchTime: 0,
            countdownTimer: null,

            // 初始化
            init: function() {
                this.lastSearchTime = this.getLastSearchTime();
                this.bindEvents();
                this.checkSearchInterval();
            },

            // 绑定事件
            bindEvents: function() {
                var self = this;
                var searchForm = document.getElementById('search-form');

                if (searchForm) {
                    searchForm.addEventListener('submit', function(e) {
                        if (!self.canSearch()) {
                            e.preventDefault();
                            self.showIntervalMessage();
                            return false;
                        }

                        // 显示加载状态
                        self.showLoadingState();

                        // 记录搜索时间
                        self.setLastSearchTime();
                    });
                }
            },

            // 显示加载状态
            showLoadingState: function() {
                var searchBtn = document.getElementById('search-btn');
                var searchLoading = document.getElementById('search-loading');

                if (searchBtn) {
                    searchBtn.classList.add('loading');
                    searchBtn.disabled = true;
                }

                if (searchLoading) {
                    searchLoading.style.display = 'flex';
                }
            },

            // 隐藏加载状态
            hideLoadingState: function() {
                var searchBtn = document.getElementById('search-btn');
                var searchLoading = document.getElementById('search-loading');

                if (searchBtn) {
                    searchBtn.classList.remove('loading');
                    searchBtn.disabled = false;
                }

                if (searchLoading) {
                    searchLoading.style.display = 'none';
                }
            },

            // 检查是否可以搜索
            canSearch: function() {
                var now = Math.floor(Date.now() / 1000);
                var timeDiff = now - this.lastSearchTime;
                return timeDiff >= this.interval;
            },

            // 获取剩余等待时间
            getRemainingTime: function() {
                var now = Math.floor(Date.now() / 1000);
                var timeDiff = now - this.lastSearchTime;
                return Math.max(0, this.interval - timeDiff);
            },

            // 显示间隔提示消息
            showIntervalMessage: function() {
                var remainingTime = this.getRemainingTime();
                if (remainingTime > 0) {
                    this.showErrorMessage('搜索过于频繁，请等待 ' + remainingTime + ' 秒后再试');
                    this.startCountdown(remainingTime);
                }
            },

            // 显示错误消息
            showErrorMessage: function(message) {
                var errorDiv = document.querySelector('.search-error-message');
                if (!errorDiv) {
                    errorDiv = document.createElement('div');
                    errorDiv.className = 'search-error-message';
                    errorDiv.style.cssText = 'background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 10px; margin-bottom: 10px; border-radius: 4px; font-size: 14px;';

                    var searchForm = document.querySelector('.search-page-form');
                    var form = document.getElementById('search-form');
                    searchForm.insertBefore(errorDiv, form);
                }

                errorDiv.innerHTML = '<i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>' +
                                    '<span id="search-error-text">' + message + '</span>' +
                                    '<span id="countdown-timer" style="font-weight: bold;"></span>';
                errorDiv.style.display = 'block';
            },

            // 隐藏错误消息
            hideErrorMessage: function() {
                var errorDiv = document.querySelector('.search-error-message');
                if (errorDiv) {
                    errorDiv.style.display = 'none';
                }
            },

            // 开始倒计时
            startCountdown: function(seconds) {
                var self = this;
                var countdownElement = document.getElementById('countdown-timer');
                var searchBtn = document.getElementById('search-btn');

                // 禁用搜索按钮
                if (searchBtn) {
                    searchBtn.disabled = true;
                    searchBtn.style.opacity = '0.6';
                }

                // 清除之前的计时器
                if (this.countdownTimer) {
                    clearInterval(this.countdownTimer);
                }

                // 更新倒计时显示
                var updateCountdown = function() {
                    if (seconds > 0) {
                        if (countdownElement) {
                            countdownElement.textContent = '（' + seconds + '秒后可再次搜索）';
                        }
                        seconds--;
                    } else {
                        // 倒计时结束
                        clearInterval(self.countdownTimer);
                        self.countdownTimer = null;

                        // 启用搜索按钮
                        if (searchBtn) {
                            searchBtn.disabled = false;
                            searchBtn.style.opacity = '1';
                        }

                        // 隐藏错误消息
                        self.hideErrorMessage();
                    }
                };

                // 立即执行一次
                updateCountdown();

                // 每秒更新
                this.countdownTimer = setInterval(updateCountdown, 1000);
            },

            // 检查搜索间隔
            checkSearchInterval: function() {
                var remainingTime = this.getRemainingTime();
                if (remainingTime > 0) {
                    this.showIntervalMessage();
                }
            },

            // 获取上次搜索时间
            getLastSearchTime: function() {
                try {
                    return parseInt(localStorage.getItem('lastSearchTime') || '0');
                } catch (e) {
                    return 0;
                }
            },

            // 设置上次搜索时间
            setLastSearchTime: function() {
                try {
                    var now = Math.floor(Date.now() / 1000);
                    localStorage.setItem('lastSearchTime', now.toString());
                    this.lastSearchTime = now;
                } catch (e) {
                    this.lastSearchTime = Math.floor(Date.now() / 1000);
                }
            }
        };

        // ===== 事件监听 =====
        // 页面加载完成
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化搜索间隔管理
            SearchInterval.init();

            // 获取当前关键词
            const keyword = getUrlParam('keyword');
            if (keyword) {
                addSearchHistory(keyword);
            }

            // 显示内容
            setTimeout(function() {
                document.documentElement.classList.remove('no-fouc');
                document.body.classList.add('content-loaded');
            }, 50);
        });

        // 如果页面已经有搜索错误，启动倒计时
        <?php if (!empty($search_error) && preg_match('/请等待 (\d+) 秒/', $search_error, $matches)): ?>
        document.addEventListener('DOMContentLoaded', function() {
            SearchInterval.startCountdown(<?php echo $matches[1]; ?>);
        });
        <?php endif; ?>
    </script>
</body>
</html> 