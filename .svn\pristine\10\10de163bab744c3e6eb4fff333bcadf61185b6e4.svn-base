<?php
/**
 * 操作日志表创建脚本
 * 执行此脚本来创建操作日志表
 */

define('IN_BTMPS', true);
require_once '../include/common.inc.php';

// 检查管理员登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header('Location: login.php');
    exit;
}

$message = '';
$error = '';

// 处理表创建
if ($_SERVER['REQUEST_METHOD'] === 'POST' && $_POST['action'] === 'create_table') {
    try {
        // 读取SQL文件
        $sql_file = 'create_operation_logs_table.sql';
        if (!file_exists($sql_file)) {
            throw new Exception('SQL文件不存在：' . $sql_file);
        }
        
        $sql_content = file_get_contents($sql_file);
        if ($sql_content === false) {
            throw new Exception('无法读取SQL文件');
        }
        
        // 分割SQL语句
        $sql_statements = explode(';', $sql_content);
        $executed_count = 0;
        
        foreach ($sql_statements as $sql) {
            $sql = trim($sql);
            if (empty($sql) || strpos($sql, '--') === 0) {
                continue;
            }
            
            // 跳过注释和空行
            if (preg_match('/^\s*(--|\/\*|$)/', $sql)) {
                continue;
            }
            
            // 执行SQL
            $result = $db->query($sql);
            if ($result === false) {
                throw new Exception('SQL执行失败: ' . $db->error() . "\nSQL: " . $sql);
            }
            
            $executed_count++;
        }
        
        $message = "操作日志表创建成功！共执行 {$executed_count} 条SQL语句。";
        
        // 记录安装日志
        $install_log_sql = "INSERT INTO operation_logs (
            user_id, user_type, username, operation_type, target_type, 
            operation_desc, ip_address, created_at
        ) VALUES (?, 'admin', ?, 'create', 'table', ?, ?, ?)";
        
        // 使用现有的数据库类方法
        $db->query(sprintf(
            "INSERT INTO operation_logs (user_id, user_type, username, operation_type, target_type, operation_desc, ip_address, created_at) VALUES (%d, 'admin', '%s', 'create', 'table', '%s', '%s', %d)",
            $_SESSION['admin']['id'] ?? 0,
            $db->escape($_SESSION['admin']['username'] ?? 'admin'),
            $db->escape('创建操作日志表和相关结构'),
            $db->escape($_SERVER['REMOTE_ADDR'] ?? '127.0.0.1'),
            time()
        ));
        
    } catch (Exception $e) {
        $error = $e->getMessage();
    }
}

// 检查表是否已存在
$table_exists = false;
$table_info = [];

try {
    $check_sql = "SHOW TABLE STATUS LIKE 'operation_logs'";
    $result = $db->query($check_sql);
    if ($result && $db->num_rows($result) > 0) {
        $table_exists = true;
        $table_info = $db->fetch_array($result);
        
        // 获取记录数
        $count_result = $db->query("SELECT COUNT(*) as total FROM operation_logs");
        if ($count_result) {
            $count_data = $db->fetch_array($count_result);
            $table_info['record_count'] = $count_data['total'];
        }
    }
} catch (Exception $e) {
    // 表不存在或其他错误
}

?>

<!DOCTYPE html>
<html>
<head>
    <title>操作日志系统安装</title>
    <meta charset="utf-8">
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 0;
            padding: 40px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: #fff;
            padding: 40px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #333;
            border-bottom: 2px solid #007cba;
            padding-bottom: 10px;
        }
        
        .status-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 6px;
            padding: 20px;
            margin: 20px 0;
        }
        
        .status-success {
            background: #d4edda;
            border-color: #c3e6cb;
            color: #155724;
        }
        
        .status-warning {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        
        .status-error {
            background: #f8d7da;
            border-color: #f5c6cb;
            color: #721c24;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #007cba;
            color: white;
            text-decoration: none;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
        }
        
        .btn:hover {
            background: #005a87;
        }
        
        .btn-success {
            background: #28a745;
        }
        
        .btn-success:hover {
            background: #218838;
        }
        
        .btn-danger {
            background: #dc3545;
        }
        
        .btn-danger:hover {
            background: #c82333;
        }
        
        .info-table {
            width: 100%;
            border-collapse: collapse;
            margin: 20px 0;
        }
        
        .info-table th,
        .info-table td {
            padding: 10px;
            text-align: left;
            border-bottom: 1px solid #dee2e6;
        }
        
        .info-table th {
            background: #f8f9fa;
            font-weight: 600;
        }
        
        .feature-list {
            list-style: none;
            padding: 0;
        }
        
        .feature-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
        }
        
        .feature-list li:before {
            content: "✓";
            color: #28a745;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .code-block {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            margin: 15px 0;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            overflow-x: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>操作日志系统安装</h1>
        
        <?php if ($message): ?>
        <div class="status-card status-success">
            <strong>成功！</strong> <?php echo htmlspecialchars($message); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($error): ?>
        <div class="status-card status-error">
            <strong>错误！</strong> <?php echo htmlspecialchars($error); ?>
        </div>
        <?php endif; ?>
        
        <?php if ($table_exists): ?>
        <div class="status-card status-success">
            <h3>✓ 操作日志表已存在</h3>
            <p>操作日志系统已经安装并可以使用。</p>
            
            <table class="info-table">
                <tr>
                    <th>表名</th>
                    <td>operation_logs</td>
                </tr>
                <tr>
                    <th>存储引擎</th>
                    <td><?php echo $table_info['Engine'] ?? 'Unknown'; ?></td>
                </tr>
                <tr>
                    <th>字符集</th>
                    <td><?php echo $table_info['Collation'] ?? 'Unknown'; ?></td>
                </tr>
                <tr>
                    <th>数据大小</th>
                    <td><?php echo round(($table_info['Data_length'] ?? 0) / 1024 / 1024, 2); ?> MB</td>
                </tr>
                <tr>
                    <th>索引大小</th>
                    <td><?php echo round(($table_info['Index_length'] ?? 0) / 1024 / 1024, 2); ?> MB</td>
                </tr>
                <tr>
                    <th>记录数</th>
                    <td><?php echo number_format($table_info['record_count'] ?? 0); ?> 条</td>
                </tr>
                <tr>
                    <th>创建时间</th>
                    <td><?php echo $table_info['Create_time'] ?? 'Unknown'; ?></td>
                </tr>
            </table>
            
            <div style="margin-top: 20px;">
                <a href="operation_logs.php" class="btn btn-success">查看操作日志</a>
                <a href="integration_example.php" class="btn">查看集成示例</a>
            </div>
        </div>
        
        <?php else: ?>
        <div class="status-card status-warning">
            <h3>⚠ 操作日志表未安装</h3>
            <p>需要创建操作日志表才能使用日志功能。</p>
        </div>
        
        <div class="status-card">
            <h3>功能特性</h3>
            <ul class="feature-list">
                <li>记录所有用户操作（创建、修改、删除、登录等）</li>
                <li>详细的IP地址和端口信息记录</li>
                <li>支持操作前后数据对比</li>
                <li>灵活的筛选和搜索功能</li>
                <li>批量删除和自动清理功能</li>
                <li>CSV格式日志导出</li>
                <li>完整的用户代理和请求信息</li>
                <li>高性能索引优化</li>
            </ul>
        </div>
        
        <div class="status-card">
            <h3>表结构说明</h3>
            <p>将创建以下数据库结构：</p>
            <div class="code-block">
operation_logs 表字段：
- id: 日志ID（主键）
- user_id: 操作用户ID
- user_type: 用户类型（admin/user/guest）
- username: 用户名
- operation_type: 操作类型（create/update/delete/login/logout）
- target_type: 操作对象类型（post/news/category等）
- target_id: 操作对象ID
- target_title: 操作对象标题
- operation_desc: 操作描述
- old_data: 修改前数据（JSON）
- new_data: 修改后数据（JSON）
- ip_address: IP地址（支持IPv6）
- port: 端口号
- user_agent: 用户代理
- referer: 来源页面
- request_method: 请求方法
- request_uri: 请求URI
- session_id: 会话ID
- status: 操作状态（成功/失败）
- error_message: 错误信息
- execution_time: 执行时间（毫秒）
- created_at: 创建时间
            </div>
        </div>
        
        <form method="POST" style="text-align: center; margin-top: 30px;">
            <input type="hidden" name="action" value="create_table">
            <button type="submit" class="btn" onclick="return confirm('确定要创建操作日志表吗？')">
                创建操作日志表
            </button>
        </form>
        <?php endif; ?>
        
        <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #dee2e6; text-align: center; color: #666;">
            <p>操作日志系统 v1.0 | 支持完整的操作审计和追踪</p>
        </div>
    </div>
</body>
</html>
