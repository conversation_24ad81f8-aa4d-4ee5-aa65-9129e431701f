<?php
if (!defined('IN_BTMPS')) { exit('Access Denied'); }
return array (
  'expire' => 1753256751,
  'data' => 
  array (
    'post' => 
    array (
      'id' => 111038,
      'user_id' => 0,
      'category_id' => 19,
      'region_id' => 84,
      'expire_days' => 7,
      'title' => '11111',
      'is_top_home' => 0,
      'is_top_category' => 0,
      'is_top_subcategory' => 0,
      'top_home_expire' => NULL,
      'top_category_expire' => NULL,
      'top_subcategory_expire' => NULL,
      'view_count' => 5,
      'image_count' => 4,
      'status' => 1,
      'created_at' => 1753076898,
      'updated_at' => 1753076898,
      'expired_at' => 1753681698,
      'is_expired' => 0,
      'category_name' => '学历教育',
      'category_pinyin' => 'xueli',
      'parent_category_name' => '教育培训',
      'parent_category_pinyin' => 'jiaoyu',
      'parent_category_id' => 14,
      'region_name' => '南京市',
      'content' => '1111111111111111111111111111111111ssadsadssadsd',
      'fields_data' => '',
      'ip' => '127.0.0.1',
      'password' => 'aa7f0839b6fab5466f44ab684dae52d9',
      'contact_name' => '阿斯顿',
      'contact_mobile' => '13120191286',
      'contact_weixin' => '',
      'contact_address' => '',
      'fields' => 
      array (
      ),
      'mobile' => '13120191286',
      'wechat' => '',
    ),
    'images' => 
    array (
      0 => 
      array (
        'id' => 166591,
        'post_id' => 111038,
        'type' => 'post',
        'file_path' => 'uploads/images/2025/0721/20250721165620_2251.jpg',
        'thumb_path' => 'uploads/images/2025/0721/thumbs/20250721165620_2251.jpg',
        'file_size' => 598189,
        'file_type' => 'image/jpeg',
        'sort_order' => 0,
        'created_at' => 1753088180,
      ),
      1 => 
      array (
        'id' => 166592,
        'post_id' => 111038,
        'type' => 'post',
        'file_path' => 'uploads/images/2025/0721/20250721165620_2351.jpg',
        'thumb_path' => 'uploads/images/2025/0721/thumbs/20250721165620_2351.jpg',
        'file_size' => 157465,
        'file_type' => 'image/jpeg',
        'sort_order' => 1,
        'created_at' => 1753088180,
      ),
      2 => 
      array (
        'id' => 166593,
        'post_id' => 111038,
        'type' => 'post',
        'file_path' => 'uploads/images/2025/0721/20250721165620_8634.jpg',
        'thumb_path' => 'uploads/images/2025/0721/thumbs/20250721165620_8634.jpg',
        'file_size' => 126794,
        'file_type' => 'image/jpeg',
        'sort_order' => 2,
        'created_at' => 1753088180,
      ),
      3 => 
      array (
        'id' => 166594,
        'post_id' => 111038,
        'type' => 'post',
        'file_path' => 'uploads/images/2025/0721/20250721165620_6815.jpg',
        'thumb_path' => 'uploads/images/2025/0721/thumbs/20250721165620_6815.jpg',
        'file_size' => 303560,
        'file_type' => 'image/jpeg',
        'sort_order' => 3,
        'created_at' => 1753088180,
      ),
    ),
    'related_posts' => 
    array (
      0 => 
      array (
        'id' => 110972,
        'title' => '全新家具回收40元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://www.fenlei.com/xueli/110972.html',
      ),
      1 => 
      array (
        'id' => 110965,
        'title' => '精品电脑安装47元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://www.fenlei.com/xueli/110965.html',
      ),
      2 => 
      array (
        'id' => 110943,
        'title' => '特价电视回收4元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://www.fenlei.com/xueli/110943.html',
      ),
      3 => 
      array (
        'id' => 110941,
        'title' => '全新空调处理18元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://www.fenlei.com/xueli/110941.html',
      ),
      4 => 
      array (
        'id' => 110933,
        'title' => '精品车辆置换81元',
        'created_at' => 1746494562,
        'updated_at' => 1746494562,
        'detail_url' => 'http://www.fenlei.com/xueli/110933.html',
      ),
    ),
    'category_info' => 
    array (
      'id' => '19',
      'parent_id' => '14',
      'name' => '学历教育',
      'icon' => 'degree.png',
      'sort_order' => '100',
      'status' => '1',
      'pinyin' => 'xueli',
      'seo_title' => NULL,
      'seo_keywords' => NULL,
      'seo_description' => NULL,
      'template' => NULL,
      'detail_template' => NULL,
      'subcategory_ids' => '',
    ),
    'cached_at' => 1753249551,
  ),
);
