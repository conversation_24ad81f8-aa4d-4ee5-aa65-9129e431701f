<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 信息管理页面
 */
// 引入公共文件
require_once('../include/common.inc.php');

// 引入信息处理函数库
require_once('../include/info.fun.php');

// 引入图片处理函数库，确保deletePostAttachments函数可用
require_once('../include/image.fun.php');

// 引入操作日志类
require_once('../include/OperationLogger.class.php');

// 检查管理员是否登录
if (!isset($_SESSION['admin']) || $_SESSION['admin']['is_login'] !== true) {
    header("Location: login.php");
    exit;
}

// 当前页面
$current_page = 'info';

// 操作类型
$action = isset($_GET['action']) ? trim($_GET['action']) : 'list';

// 信息提示
$message = '';
$error = '';

// 修改模板目录为admin/template目录
$tpl->setTemplateDir(dirname(__FILE__) . '/template/');

// 每页显示数量
$items_per_page = 10;

// 获取当前页码
$current_page = isset($_GET['page']) ? intval($_GET['page']) : 1;
if ($current_page < 1) {
    $current_page = 1;
}

// 根据操作类型执行相应操作
switch ($action) {
    // 删除图片API
    case 'delete_image':
        // 检查是否为Ajax请求
        if (!empty($_SERVER['HTTP_X_REQUESTED_WITH']) && strtolower($_SERVER['HTTP_X_REQUESTED_WITH']) == 'xmlhttprequest') {
            header('Content-Type: application/json');
            
            // 获取图片ID和信息ID
            $imageId = isset($_POST['id']) ? intval($_POST['id']) : 0;
            $postId = isset($_POST['post_id']) ? intval($_POST['post_id']) : 0;
            
            if (!$imageId || !$postId) {
                echo json_encode([
                    'success' => false,
                    'message' => '参数错误：缺少图片ID或信息ID'
                ]);
                exit;
            }
            
            // 删除图片
            if (deleteImage($imageId)) {
                // 更新图片数量
                updateImageCount($postId);
                
                echo json_encode([
                    'success' => true,
                    'message' => '图片删除成功'
                ]);
            } else {
                echo json_encode([
                    'success' => false,
                    'message' => '删除图片失败，请稍后重试'
                ]);
            }
            exit;
        }
        break;
        
    // 信息列表
    case 'list':
        // 获取筛选条件
        $category_id = isset($_GET['category_id']) ? intval($_GET['category_id']) : 0;
        $region_id = isset($_GET['region_id']) ? intval($_GET['region_id']) : 0;
        
        // 检查是否有明确的筛选条件
        $has_filter = isset($_GET['status']) || isset($_GET['is_expired']) || isset($_GET['all']) || isset($_GET['mobile']);
        
            // 默认显示正常信息（已上架且未过期）
    $status = isset($_GET['status']) ? intval($_GET['status']) : ($has_filter ? -1 : 1);
    $is_expired = isset($_GET['is_expired']) ? intval($_GET['is_expired']) : 0;
        
        $keyword = isset($_GET['keyword']) ? trim($_GET['keyword']) : '';
        $mobile = isset($_GET['mobile']) ? trim($_GET['mobile']) : '';

        // 获取分页信息列表
        $result = get_filtered_posts($current_page, $items_per_page, $category_id, $region_id, $status, $keyword, $is_expired, $mobile);
        $posts = $result['posts'];
        $total_items = $result['total'];

        // 计算总页数
        $total_pages = ceil($total_items / $items_per_page);
        if ($current_page > $total_pages && $total_pages > 0) {
            $current_page = $total_pages;
        }

        // 构建分页链接
        $pagination = build_pagination($current_page, $total_pages, $total_items, $items_per_page);

        // 获取所有分类作为筛选选项
        $categories_raw = get_all_categories();
        $categories = build_category_tree($categories_raw);

        // 获取所有区域作为筛选选项
        $regions = get_all_regions();
        $regions = build_region_tree($regions);

        // 确保GET参数正确传递到模板
        $id_value = isset($_GET['id']) ? $_GET['id'] : '';
        $tpl->assign('id_value', $id_value);
        
        // 传递数据到模板
        $tpl->assign('posts', $posts);
        $tpl->assign('pagination', $pagination);
        $tpl->assign('categories', $categories);
        $tpl->assign('categories_raw', $categories_raw);  // 原始分类数据用于JavaScript联动
        $tpl->assign('regions', $regions);
        $tpl->assign('category_id', $category_id);
        $tpl->assign('region_id', $region_id);
        $tpl->assign('status', $status);
        $tpl->assign('keyword', $keyword);
        $tpl->assign('is_expired', $is_expired);
        $tpl->assign('mobile', $mobile);
        break;
        
    // 添加信息
    case 'add':
        // 获取所有分类
        $categories = get_all_categories();
        $categories = build_category_tree($categories);
        
        // 获取所有区域
        $regions = get_all_regions();
        $regions = build_region_tree($regions);

        // 处理区域数据，为没有有效子区域的省份添加省份本身作为选项
        $regions = processRegionsForPost($regions);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {
            // 处理表单提交
            $result = save_post();
            if ($result['success']) {
                $message = '信息添加成功';
                $new_id = $result['id']; // 获取新添加的信息ID

                // 记录操作日志
                $logger = new OperationLogger($db);
                $logger->log([
                    'operation_type' => 'create',
                    'target_type' => 'post',
                    'target_id' => $new_id,
                    'operation_desc' => '创建信息',
                    'target_title' => isset($_POST['title']) ? $_POST['title'] : '',
                    'user_id' => $_SESSION['admin']['id'],
                    'username' => $_SESSION['admin']['username'],
                    'user_type' => 'admin'
                ]);

                // 清理相关缓存（新建信息，需要清理首页缓存）
                $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : null;
                clearPostRelatedCaches($new_id, $category_id, 'create');

                // 清理置顶相关缓存（新建信息，没有旧状态）
                $new_top_status = array(
                    'home' => isset($_POST['is_top_home']) ? true : false,
                    'category' => isset($_POST['is_top_category']) ? true : false,
                    'subcategory' => isset($_POST['is_top_subcategory']) ? true : false
                );

                // 如果有任何置顶设置，清理相应缓存
                if ($new_top_status['home'] || $new_top_status['category'] || $new_top_status['subcategory']) {
                    clearTopRelatedCaches($new_id, $category_id, null, $new_top_status);
                }

                // 检查是否点击的是"保存并返回列表"按钮
                if (isset($_POST['save_and_back'])) {
                    // 重定向到列表页
                    header("Location: info.php?message=" . urlencode($message));
                    exit;
                } else {
                    // 重定向到编辑页面，以便继续编辑，并显示保存成功弹窗
                    header("Location: info.php?action=edit&id=" . $new_id . "&message=" . urlencode($message) . "&saved=1");
                    exit;
                }
            } else {
                $error = $result['error'];
            }
        }
        
        // 初始化post变量，确保包含所有模板中使用的键
        $post = array(
            'title' => '',
            'category_id' => 0,
            'region_id' => 0,
            'status' => 1, // 默认为正常状态
            'expire_days' => 30,
            'content' => '',
            'contact_name' => '',
            'contact_mobile' => '',
            'contact_weixin' => '',
            'contact_address' => ''
        );
        
        $tpl->assign('post', $post);
        $tpl->assign('categories', $categories);
        $tpl->assign('regions', $regions);
        break;
        
    // 编辑信息
    case 'edit':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定信息ID';
            break;
        }
        
        // 获取信息详情
        $post = get_post_detail($id);
        if (!$post) {
            $error = '信息不存在';
            break;
        }
        
        // 获取所有分类
        $categories = get_all_categories();
        $categories = build_category_tree($categories);
        
        // 获取所有区域
        $regions = get_all_regions();
        $regions = build_region_tree($regions);

        // 处理区域数据，为没有有效子区域的省份添加省份本身作为选项
        $regions = processRegionsForPost($regions);
        
        if ($_SERVER['REQUEST_METHOD'] === 'POST') {

            // 处理表单提交
            $result = save_post($id);
            if ($result['success']) {
                $message = '信息更新成功';

                // 记录操作日志
                $logger = new OperationLogger($db);
                $logger->log([
                    'operation_type' => 'update',
                    'target_type' => 'post',
                    'target_id' => $id,
                    'operation_desc' => '修改信息',
                    'target_title' => isset($_POST['title']) ? $_POST['title'] : '',
                    'user_id' => $_SESSION['admin']['id'],
                    'username' => $_SESSION['admin']['username'],
                    'user_type' => 'admin'
                ]);

                // 清理相关缓存（修改信息，不需要清理首页缓存）
                $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : null;
                clearPostRelatedCaches($id, $category_id, 'update');

                // 清理置顶相关缓存（只有状态发生变化时才清理）
                clearTopRelatedCaches($id, $category_id, $old_top_status, $new_top_status);

                // 检查是否点击的是"保存并返回列表"按钮
                if (isset($_POST['save_and_back'])) {
                    // 重定向到列表页
                    header("Location: info.php?message=" . urlencode($message));
                    exit;
                } else {
                    // 重定向回编辑页面，保持继续编辑，并显示保存成功弹窗
                    header("Location: info.php?action=edit&id=" . $id . "&message=" . urlencode($message) . "&saved=1");
                    exit;
                }
            } else {
                $error = $result['error'];
            }
        }
        
        // 传递附件信息到模板
        $tpl->assign('attachments', $post['attachments']);
        
        $tpl->assign('post', $post);
        $tpl->assign('categories', $categories);
        $tpl->assign('regions', $regions);
        break;
        
    // 删除信息
    case 'delete':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        if (!$id) {
            $error = '未指定信息ID';
            break;
        }
        
        // 获取信息详情用于日志记录
        $post_info = get_post_detail($id);
        $post_title = $post_info ? $post_info['title'] : '';

        // 删除信息
        if (delete_post($id)) {
            $message = '信息删除成功';

            // 记录操作日志
            $logger = new OperationLogger($db);
            $logger->log([
                'operation_type' => 'delete',
                'target_type' => 'post',
                'target_id' => $id,
                'operation_desc' => '删除信息',
                'target_title' => $post_title,
                'user_id' => $_SESSION['admin']['id'],
                'username' => $_SESSION['admin']['username'],
                'user_type' => 'admin'
            ]);

            // 重定向到列表页
            header("Location: info.php?message=" . urlencode($message));
            exit;
        } else {
            $error = '删除信息失败';
        }
        break;
        
    // 批量删除信息
    case 'batch_delete':
        // 检查是否有选中的信息ID
        if (!isset($_POST['post_ids']) || !is_array($_POST['post_ids']) || empty($_POST['post_ids'])) {
            $error = '未选择要删除的信息';
            break;
        }
        
        $post_ids = array_map('intval', $_POST['post_ids']);
        $success_count = 0;
        $error_count = 0;
        $logger = new OperationLogger($db);

        foreach ($post_ids as $id) {
            // 获取信息详情用于日志记录
            $post_info = get_post_detail($id);
            $post_title = $post_info ? $post_info['title'] : '';

            if (delete_post($id)) {
                $success_count++;

                // 记录操作日志
                $logger->log([
                    'operation_type' => 'delete',
                    'target_type' => 'post',
                    'target_id' => $id,
                    'operation_desc' => '批量删除信息',
                    'target_title' => $post_title,
                    'user_id' => $_SESSION['admin']['id'],
                    'username' => $_SESSION['admin']['username'],
                    'user_type' => 'admin'
                ]);
            } else {
                $error_count++;
            }
        }
        
        // 提示信息
        if ($success_count > 0) {
            $message = "成功删除 {$success_count} 条信息";
            if ($error_count > 0) {
                $message .= "，{$error_count} 条信息删除失败";
            }
        } else {
            $message = "删除失败";
        }
        
        // 重定向到列表页
        header("Location: info.php?message=" . urlencode($message));
        exit;
        
    // 更改状态
    case 'toggle_status':
        $id = isset($_GET['id']) ? intval($_GET['id']) : 0;
        $status = isset($_GET['to']) ? intval($_GET['to']) : 1; // 默认设为正常状态
        
        if (!$id) {
            $error = '未指定信息ID';
            break;
        }
        
        // 获取信息详情用于日志记录
        $post_info = get_post_detail($id);
        $post_title = $post_info ? $post_info['title'] : '';

        // 更改状态
        if (update_post_status($id, $status)) {
            $status_text = $status == 1 ? '已启用' : ($status == 0 ? '审核中' : '已下架');
            $message = '信息状态已更改为' . $status_text;

            // 记录操作日志
            $logger = new OperationLogger($db);
            $logger->log([
                'operation_type' => 'update',
                'target_type' => 'post',
                'target_id' => $id,
                'operation_desc' => '更改信息状态为' . $status_text,
                'target_title' => $post_title,
                'user_id' => $_SESSION['admin']['id'],
                'username' => $_SESSION['admin']['username'],
                'user_type' => 'admin'
            ]);

            // 重定向到列表页
            header("Location: info.php?message=" . urlencode($message));
            exit;
        } else {
            $error = '更改信息状态失败';
        }
        break;
}

// 获取传递的消息
if (isset($_GET['message']) && empty($message)) {
    $message = $_GET['message'];
}

// 传递数据到模板
$tpl->assign('current_page', $current_page);
$tpl->assign('breadcrumb', '信息管理');
$tpl->assign('message', $message);
$tpl->assign('error', $error);
$tpl->assign('action', $action);
$tpl->assign('page_title', '信息管理');
$tpl->assign('admin', $_SESSION['admin']);

// 根据不同操作分配特定数据
switch ($action) {
    case 'list':
        $tpl->display('info_list.htm');
        break;
        
    case 'add':
        $tpl->display('info_edit.htm');
        break;
        
    case 'edit':
        $tpl->display('info_edit.htm');
        break;
        
    default:
        $tpl->display('info_list.htm');
        break;
}

/**
 * 获取所有分类
 */
function get_all_categories() {
    // 直接使用全局预加载的缓存数据
    $categories = $GLOBALS['cached_categories'];
    
    // 只保留排序逻辑
    usort($categories, function($a, $b) {
        if ($a['sort_order'] == $b['sort_order']) {
            return $a['id'] - $b['id'];
        }
        return $b['sort_order'] - $a['sort_order'];
    });
    
    return $categories;
}

/**
 * 获取所有区域
 */
function get_all_regions() {
    // 直接使用全局预加载的缓存数据
    return $GLOBALS['cached_regions'];
}

/**
 * 根据条件获取信息列表
 */
function get_filtered_posts($page, $limit, $category_id = 0, $region_id = 0, $status = -1, $keyword = '', $is_expired = 0, $mobile = '') {
    global $db;
    
    // 限制为整数
    $page = max(1, intval($page));
    $limit = max(1, intval($limit));
    $offset = ($page - 1) * $limit;
    
    // 构建查询条件
    $conditions = array();
    $params = array();
    
    // 添加ID筛选条件 - 直接处理ID参数
    $id_filter = 0;
    if (isset($_GET['id']) && $_GET['id'] !== '') {
        $id_filter = intval($_GET['id']);
    }
    
    // 检查是否有ID或关键词搜索
    $has_id_or_keyword = ($id_filter > 0 || !empty($keyword));
    
    if ($id_filter > 0) {
        $conditions[] = "p.id = ?";
        $params[] = $id_filter;
        error_log("按ID搜索: " . $id_filter);
    }
    // 如果没有ID筛选，但有关键字，使用标题和内容搜索
    elseif (!empty($keyword)) {
        // 修改搜索逻辑，同时搜索posts表的标题和post_contents表的内容
        $conditions[] = "(p.title LIKE ? OR pc.content LIKE ?)";
        $keyword_param = "%" . $keyword . "%";
        $params[] = $keyword_param;
        $params[] = $keyword_param;
    }
    
    // 添加分类筛选条件
    if ($category_id > 0) {
        // 检查是否有子分类选择
        $sub_category_id = isset($_GET['sub_category_id']) ? intval($_GET['sub_category_id']) : 0;
        if ($sub_category_id > 0) {
            // 如果选择了子分类，只使用子分类ID
            $conditions[] = "p.category_id = ?";
            $params[] = $sub_category_id;
            error_log("使用子分类筛选: category_id = " . $sub_category_id);
        } else {
            // 获取所有分类数据
            $categories = get_all_categories();
            error_log("父分类ID: " . $category_id);
            error_log("获取到的所有分类数量: " . count($categories));
            
            // 如果只选择了父分类，获取所有子分类ID
            $sub_categories = array_filter($categories, function($cat) use ($category_id) {
                return isset($cat['parent_id']) && intval($cat['parent_id']) == $category_id;
            });
            error_log("找到的子分类数量: " . count($sub_categories));
            
            // 构建分类ID数组，包含父分类和所有子分类
            $category_ids = array($category_id);
            if (!empty($sub_categories)) {
                foreach ($sub_categories as $sub_cat) {
                    if (isset($sub_cat['id'])) {
                        $category_ids[] = intval($sub_cat['id']);
                        error_log("添加子分类ID: " . $sub_cat['id']);
                    }
                }
            }
            error_log("最终的分类ID数组: " . implode(',', $category_ids));
            
            // 使用IN条件查询所有相关分类的信息
            $placeholders = str_repeat('?,', count($category_ids) - 1) . '?';
            $conditions[] = "p.category_id IN ($placeholders)";
            $params = array_merge($params, array_values($category_ids));
        }
    }
    
    // 如果有ID或关键词搜索，不添加状态筛选，在所有信息中搜索
    if (!$has_id_or_keyword) {
        if ($status >= 0) {
            $conditions[] = "p.status = ?";
            $params[] = $status;
            
            // 如果是正常状态，还需要检查是否过期
            if ($status == 1) {
                $conditions[] = "(p.expired_at > ? OR p.expired_at = 0)";
                $params[] = time();
            }
        }
        
        if ($is_expired == 1) {
            $conditions[] = "p.expired_at < ? AND p.expired_at > 0";
            $params[] = time();
        }
    }
    
    if (!empty($mobile)) {
        // 手机号现在在post_contents表中
        $conditions[] = "pc.contact_mobile LIKE ?";
        $params[] = "%" . $mobile . "%";
        // 确保联接post_contents表
        $join_clause = "INNER JOIN post_contents pc ON p.id = pc.post_id";
    }
    
    $where_clause = empty($conditions) ? "" : "WHERE " . implode(" AND ", $conditions);
    
    // 如果需要搜索内容或手机号，添加与post_contents表的联接
    $join_clause = "";
    if (!empty($keyword) || !empty($mobile)) {
        $join_clause = "INNER JOIN post_contents pc ON p.id = pc.post_id";
    }
    
    // 查询总数
    $count_sql = "SELECT COUNT(*) as total FROM posts p $join_clause $where_clause";
    $count_result = $db->query($count_sql, $params);
    $total = 0;
    if ($row = $db->fetch_array($count_result)) {
        $total = $row['total'];
    }
    
    // 查询数据 - 只选择必要的字段，同时查询分类拼音和联系人信息
    $sql = "SELECT p.id, p.title, p.category_id, p.status, 
            p.updated_at, p.expired_at, p.created_at, p.image_count,
            pc.contact_name, pc.contact_mobile
            FROM posts p 
            INNER JOIN post_contents pc ON p.id = pc.post_id 
            $where_clause 
            ORDER BY p.id DESC 
            LIMIT ?, ?";
    
    $all_params = array_merge($params, array($offset, $limit));
    $result = $db->query($sql, $all_params);
    
    $posts = array();
    while ($row = $db->fetch_array($result)) {
        // 使用缓存获取分类信息
        $row['category_name'] = '';
        $row['category_pinyin'] = '';
        foreach ($GLOBALS['cached_categories'] as $category) {
            if ($category['id'] == $row['category_id']) {
                $row['category_name'] = $category['name'];
                $row['category_pinyin'] = isset($category['pinyin']) ? $category['pinyin'] : '';
                break;
            }
        }
        
        // 处理状态显示
        $row['status_text'] = get_status_text($row['status']);
        
        // 格式化时间
        $row['updated_time'] = date('Y-m-d H:i:s', $row['updated_at']);
        $row['expired_time'] = $row['expired_at'] ? date('Y-m-d H:i:s', $row['expired_at']) : '-';
        
        // 添加详情链接
        if (!empty($row['category_pinyin'])) {
            $row['detail_url'] = buildCategoryUrl($row['category_pinyin'], $row['id']);
        } else {
            $row['detail_url'] = "/view.php?id=" . $row['id'];
        }
        
        $posts[] = $row;
    }
    
    return array(
        'posts' => $posts,
        'total' => $total
    );
}

/**
 * 获取信息详情
 */
function get_post_detail($id) {
    global $db;
    
    $sql = "SELECT p.*, c.name as category_name, c.pinyin as category_pinyin, r.name as region_name 
            FROM posts p 
            LEFT JOIN categories c ON p.category_id = c.id 
            LEFT JOIN regions r ON p.region_id = r.id 
            WHERE p.id = ?";
    $result = $db->query($sql, array($id));
    
    if ($row = $db->fetch_array($result)) {
        // 从post_contents表中获取内容、自定义字段数据和联系人信息
        $content_sql = "SELECT content, fields_data, ip,
                      password, contact_name, contact_mobile, 
                      contact_weixin, contact_address 
                      FROM post_contents WHERE post_id = ?";
        $content_result = $db->query($content_sql, array($id));
        
        if ($content_result && $content_row = $db->fetch_array($content_result)) {
            // 将内容、联系人信息等从post_contents数据添加到posts数据中
            $row['content'] = $content_row['content'];
            $row['fields_data'] = $content_row['fields_data'];
            $row['ip'] = $content_row['ip'];
            
            // 添加联系人信息
            $row['contact_name'] = $content_row['contact_name'];
            $row['contact_mobile'] = $content_row['contact_mobile'];
            $row['contact_weixin'] = $content_row['contact_weixin'];
            $row['contact_address'] = $content_row['contact_address'];
            $row['password'] = $content_row['password'];
            
            // 调试信息
            error_log("从数据库加载内容 ID: {$id}, 内容长度: " . strlen($row['content']));
        } else {
            // 如果在post_contents表中找不到数据，设置默认值
            $row['content'] = '';
            $row['fields_data'] = '';
            $row['ip'] = '';
            
            // 设置联系人信息默认值
            $row['contact_name'] = '';
            $row['contact_mobile'] = '';
            $row['contact_weixin'] = '';
            $row['contact_address'] = '';
            $row['password'] = '';
            
            // 调试信息
            error_log("未找到内容数据 ID: {$id}");
        }
        
        // 确保内容字段不为null
        if ($row['content'] === null) {
            $row['content'] = '';
            error_log("内容为null，设为空字符串 ID: {$id}");
        }
        
        // 处理自定义字段数据
        if (!empty($row['fields_data'])) {
            $row['fields'] = json_decode($row['fields_data'], true);
        } else {
            $row['fields'] = array();
        }
        
        // 获取附件信息
        $sql = "SELECT * FROM attachments WHERE post_id = ? ORDER BY sort_order ASC, id ASC";
        $result = $db->query($sql, array($id));
        $attachments = array();
        while ($attachment = $db->fetch_array($result)) {
            // 修正文件路径，确保不包含admin目录
            $attachment['file_path'] = preg_replace('/^\/admin/', '', $attachment['file_path']);
            $attachment['thumb_path'] = preg_replace('/^\/admin/', '', $attachment['thumb_path']);
            
            // 确保路径以斜杠开头
            if (substr($attachment['file_path'], 0, 1) !== '/') {
                $attachment['file_path'] = '/' . $attachment['file_path'];
            }
            if (substr($attachment['thumb_path'], 0, 1) !== '/') {
                $attachment['thumb_path'] = '/' . $attachment['thumb_path'];
            }
            
            $attachments[] = $attachment;
        }
        $row['attachments'] = $attachments;
        
        return $row;
    }
    
    return false;
}

/**
 * 获取状态文本
 */
function get_status_text($status) {
    switch ($status) {
        case 0:
            return '待审核';
        case 1:
            return '正常';
        case 2:
            return '已下架';
        case 3:
            return '用户删除';
        default:
            return '未知';
    }
}

/**
 * 构建分页
 */
function build_pagination($current_page, $total_pages, $total_items, $items_per_page) {
    // 获取当前的所有GET参数
    $params = $_GET;
    
    // 构建基础URL参数
    unset($params['page']); // 移除page参数，后面会重新添加
    $query_string = '';
    if (!empty($params)) {
        $query_string = http_build_query($params) . '&';
    }
    
    // 更新URL模式，包含所有参数
    $url_pattern = '?' . $query_string . 'page=%d';
    
    // 计算显示的记录范围
    $start = ($current_page - 1) * $items_per_page + 1;
    $end = min($current_page * $items_per_page, $total_items);
    
    // 构建前后页链接 - 确保只使用一个占位符
    $first_link = str_replace('%d', '1', $url_pattern);
    $previous_link = str_replace('%d', max(1, $current_page - 1), $url_pattern);
    $next_link = str_replace('%d', min($total_pages, $current_page + 1), $url_pattern);
    $last_link = str_replace('%d', $total_pages, $url_pattern);
    
    // 构建页码链接
    $page_links = array();
    $num_links_to_display = 5; // 显示的页码数量
    
    $start_page = max(1, $current_page - floor($num_links_to_display / 2));
    $end_page = min($total_pages, $start_page + $num_links_to_display - 1);
    
    if ($end_page - $start_page + 1 < $num_links_to_display) {
        $start_page = max(1, $end_page - $num_links_to_display + 1);
    }
    
    for ($i = $start_page; $i <= $end_page; $i++) {
        $page_links[$i] = str_replace('%d', $i, $url_pattern);
    }
    
    // 构建分页数据
    return array(
        'current_page' => $current_page,
        'total_pages' => $total_pages,
        'total_items' => $total_items,
        'items_per_page' => $items_per_page,
        'start' => $start,
        'end' => $end,
        'first_link' => $first_link,
        'previous_link' => $previous_link,
        'next_link' => $next_link,
        'last_link' => $last_link,
        'page_links' => $page_links
    );
}

/**
 * 构建分类树
 */
function build_category_tree($categories) {
    $tree = array();
    $parent_categories = array();
    $child_categories = array();

    // 分离父分类和子分类
    foreach ($categories as $category) {
        if ($category['parent_id'] == 0) {
            $parent_categories[] = $category;
        } else {
            $child_categories[] = $category;
        }
    }

    // 为每个父分类添加子分类
    foreach ($parent_categories as $parent) {
        $parent['children'] = array();
        foreach ($child_categories as $child) {
            if ($child['parent_id'] == $parent['id']) {
                $parent['children'][] = $child;
            }
        }
        $tree[] = $parent;
    }

    return $tree;
}

/**
 * 构建区域树
 */
function build_region_tree($regions) {
    $tree = array();
    foreach ($regions as $region) {
        $region['level'] = 0;
        $tree[] = $region;
    }
    return $tree;
}

/**
 * 更新信息的图片数量
 * @param int $postId 信息ID
 * @return int 更新后的图片数量
 */
function updateImageCount($postId) {
    global $db;
    
    // 获取附件数量
    $sql = "SELECT COUNT(*) as total FROM attachments WHERE post_id = ?";
    $result = $db->query($sql, [$postId]);
    $count = 0;
    
    if ($row = $db->fetch_array($result)) {
        $count = intval($row['total']);
    }
    
    // 更新posts表中的图片数量
    $update_sql = "UPDATE posts SET image_count = ? WHERE id = ?";
    $db->query($update_sql, [$count, $postId]);
    
    return $count;
}

/**
 * 保存信息
 */
function save_post($id = 0) {
    global $db;
    
    // 获取表单数据
    $title = isset($_POST['title']) ? trim($_POST['title']) : '';
    $category_id = isset($_POST['category_id']) ? intval($_POST['category_id']) : 0;
    $region_id = isset($_POST['region_id']) ? intval($_POST['region_id']) : 0;
    $expire_days = isset($_POST['expire_days']) ? intval($_POST['expire_days']) : 30;
    $content = isset($_POST['content']) ? trim($_POST['content']) : '';
    $contact_name = isset($_POST['contact_name']) ? trim($_POST['contact_name']) : '';
    $contact_mobile = isset($_POST['contact_mobile']) ? trim($_POST['contact_mobile']) : '';
    $contact_weixin = isset($_POST['contact_weixin']) ? trim($_POST['contact_weixin']) : '';
    $contact_address = isset($_POST['contact_address']) ? trim($_POST['contact_address']) : '';
    $status = isset($_POST['status']) ? intval($_POST['status']) : 1;

    // 获取置顶设置
    $is_top_home = isset($_POST['is_top_home']) ? 1 : 0;
    $is_top_category = isset($_POST['is_top_category']) ? 1 : 0;
    $is_top_subcategory = isset($_POST['is_top_subcategory']) ? 1 : 0;

    // 定义新的置顶状态（用于缓存清理）
    $new_top_status = array(
        'home' => (bool)$is_top_home,
        'category' => (bool)$is_top_category,
        'subcategory' => (bool)$is_top_subcategory
    );

    // 获取原始置顶状态（用于编辑时的缓存清理）
    $old_top_status = array('home' => false, 'category' => false, 'subcategory' => false);
    if ($id > 0) {
        $sql = "SELECT is_top_home, is_top_category, is_top_subcategory FROM posts WHERE id = ?";
        $result_old = $db->query($sql, [$id]);
        if ($result_old && $row = $db->fetch_array($result_old)) {
            $old_top_status = array(
                'home' => (bool)$row['is_top_home'],
                'category' => (bool)$row['is_top_category'],
                'subcategory' => (bool)$row['is_top_subcategory']
            );
        }
    }

    // 处理到期时间
    $top_home_expire = null;
    $top_category_expire = null;
    $top_subcategory_expire = null;

    if ($is_top_home && !empty($_POST['top_home_expire'])) {
        $top_home_expire = strtotime($_POST['top_home_expire']);
        // 验证时间是否有效且在未来
        if ($top_home_expire === false || $top_home_expire <= time()) {
            $top_home_expire = null;
        }
    }

    if ($is_top_category && !empty($_POST['top_category_expire'])) {
        $top_category_expire = strtotime($_POST['top_category_expire']);
        if ($top_category_expire === false || $top_category_expire <= time()) {
            $top_category_expire = null;
        }
    }

    if ($is_top_subcategory && !empty($_POST['top_subcategory_expire'])) {
        $top_subcategory_expire = strtotime($_POST['top_subcategory_expire']);
        if ($top_subcategory_expire === false || $top_subcategory_expire <= time()) {
            $top_subcategory_expire = null;
        }
    }

    // 获取IP地址（用于新增时）
    $ip = get_client_ip();
    
    // 验证必填字段
    if (empty($title)) {
        return array('success' => false, 'error' => '请输入信息标题');
    }
    
    if ($category_id <= 0) {
        return array('success' => false, 'error' => '请选择信息分类');
    }
    
    // 调试信息
    error_log("保存内容 ID: {$id}, 内容长度: " . strlen($content));
    
    // 计算过期时间
    $now = time();
    $expire_at = $now + ($expire_days * 86400); // 一天有86400秒
    
    // 处理附件
    $attachments = array();
    if (isset($_POST['attachments']) && is_array($_POST['attachments'])) {
        $attachments = $_POST['attachments'];
    }

    // 处理新上传的图片
    if (isset($_FILES['images']) && !empty($_FILES['images']['name'][0])) {
        // 使用saveImages函数处理图片上传
        saveImages($id);
    }

    // 更新附件排序
    if (!empty($attachments)) {
        foreach ($attachments as $index => $attachment) {
            $file_id = isset($attachment['id']) ? intval($attachment['id']) : 0;
            $sort_order = $index + 1; // 从1开始的排序
            
            if ($file_id > 0) {
                // 更新附件排序
                $sql = "UPDATE attachments SET sort_order = ? WHERE id = ? AND post_id = ?";
                $db->query($sql, array($sort_order, $file_id, $id));
            }
        }
    }

    // 重新计算图片数量
    $sql = "SELECT COUNT(*) as total FROM attachments WHERE post_id = ?";
    $result = $db->query($sql, array($id));
    $row = $db->fetch_array($result);
    $image_count = intval($row['total']);

    // 更新图片数量
    $sql = "UPDATE posts SET image_count = ? WHERE id = ?";
    $db->query($sql, array($image_count, $id));
    
    // 获取管理密码
    $password = isset($_POST['password']) ? trim($_POST['password']) : '';
    if (empty($password)) {
        // 生成随机密码
        $password = substr(md5(uniqid(rand(), true)), 0, 8);
    }
    // MD5加密密码
    $password = md5($password);
    
    // 开始事务
    $db->beginTransaction();
    
    try {
        if ($id > 0) {
            // 更新现有信息
            
            // 获取原始分类ID
            $original_category_id = 0;
            $sql = "SELECT category_id FROM posts WHERE id = ?";
            $result = $db->query($sql, [$id]);
            if ($result && $post_data = $db->fetch_array($result)) {
                $original_category_id = intval($post_data['category_id']);
            }
            
            // 检查是否需要更新时间字段
            $update_time = isset($_POST['update_time']) && $_POST['update_time'] ? true : false;
            $update_expire = isset($_POST['update_expire']) && $_POST['update_expire'] ? true : false;
            
            // 如果不更新更新时间，则获取原记录的更新时间
            $current_updated_at = $now;
            $current_expired_at = $expire_at;
            
            if (!$update_time || !$update_expire) {
                $sql = "SELECT updated_at, expired_at FROM posts WHERE id = ?";
                $result = $db->query($sql, [$id]);
                if ($post_data = $db->fetch_array($result)) {
                    // 如果不更新更新时间，则保留原值
                    if (!$update_time) {
                        $current_updated_at = $post_data['updated_at'];
                    }
                    
                    // 如果不更新到期时间，则保留原值
                    if (!$update_expire) {
                        $current_expired_at = $post_data['expired_at'];
                    }
                }
            }
            
            // 更新posts表（包含置顶设置）
            $sql = "UPDATE posts SET
                    title = ?,
                    category_id = ?,
                    region_id = ?,
                    status = ?,
                    expire_days = ?,
                    updated_at = ?,
                    expired_at = ?,
                    image_count = ?,
                    is_top_home = ?,
                    is_top_category = ?,
                    is_top_subcategory = ?,
                    top_home_expire = ?,
                    top_category_expire = ?,
                    top_subcategory_expire = ?
                    WHERE id = ?";

            $result = $db->query($sql, array(
                $title,
                $category_id,
                $region_id,
                $status,
                $expire_days,
                $current_updated_at, // 使用根据勾选状态决定的时间
                $current_expired_at, // 使用根据勾选状态决定的时间
                $image_count,
                $is_top_home,
                $is_top_category,
                $is_top_subcategory,
                $top_home_expire,
                $top_category_expire,
                $top_subcategory_expire,
                $id
            ));
            
            // 更新post_contents表中的内容和联系人信息
            $check_content_sql = "SELECT id FROM post_contents WHERE post_id = ?";
            $check_result = $db->query($check_content_sql, [$id]);
            
            if ($check_result && $db->fetch_array($check_result)) {
                // 更新现有记录
                $content_sql = "UPDATE post_contents SET 
                               content = ?,
                               contact_name = ?,
                               contact_mobile = ?,
                               contact_weixin = ?,
                               contact_address = ?,
                               password = ?
                               WHERE post_id = ?";
                $content_result = $db->query($content_sql, [
                    $content,
                    $contact_name,
                    $contact_mobile,
                    $contact_weixin,
                    $contact_address,
                    $password,
                    $id
                ]);
            } else {
                // 插入新记录，包含IP地址和联系人信息
                $content_sql = "INSERT INTO post_contents (
                              post_id, content, ip,
                              password, contact_name, contact_mobile, 
                              contact_weixin, contact_address
                              ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
                $content_result = $db->query($content_sql, [
                    $id, $content, $ip,
                    $password, $contact_name, $contact_mobile,
                    $contact_weixin, $contact_address
                ]);
            }
            
            if (!$result || !$content_result) {
                throw new Exception('更新信息失败');
            }
            
            // 提交事务
            $db->commit();
            
        } else {
            // 添加新信息（包含置顶设置）
            $sql = "INSERT INTO posts (
                    title,
                    category_id,
                    region_id,
                    status,
                    expire_days,
                    created_at,
                    updated_at,
                    expired_at,
                    image_count,
                    is_top_home,
                    is_top_category,
                    is_top_subcategory,
                    top_home_expire,
                    top_category_expire,
                    top_subcategory_expire
                ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)";

            $result = $db->query($sql, array(
                $title,
                $category_id,
                $region_id,
                $status,
                $expire_days,
                $now,
                $now,
                $expire_at,
                $image_count,
                $is_top_home,
                $is_top_category,
                $is_top_subcategory,
                $top_home_expire,
                $top_category_expire,
                $top_subcategory_expire
            ));
            
            if (!$result) {
                throw new Exception('添加信息失败');
            }
            
            // 获取新添加的信息ID
            $id = $db->lastInsertId();
            
            // 插入到post_contents表，包含IP地址和联系人信息
            $content_sql = "INSERT INTO post_contents (
                          post_id, content, ip,
                          password, contact_name, contact_mobile, 
                          contact_weixin, contact_address
                          ) VALUES (?, ?, ?, ?, ?, ?, ?, ?)";
            $content_result = $db->query($content_sql, [
                $id, $content, $ip,
                $password, $contact_name, $contact_mobile,
                $contact_weixin, $contact_address
            ]);
            
            if (!$content_result) {
                throw new Exception('添加信息内容失败: ' . $db->error());
            }
            
            // 提交事务
            $db->commit();
        }
        
        // 更新图片数量
        updateImageCount($id);

        // 清理相关缓存
        if (function_exists('clearPostRelatedCaches')) {
            // 根据操作类型决定缓存清理策略
            $operation = ($id > 0) ? 'update' : 'create';
            clearPostRelatedCaches($id, $category_id, $operation);
        }

        // 特别处理置顶状态变化的缓存清理
        if ($id > 0 && function_exists('clearTopRelatedCaches')) {
            // 编辑信息时，检查置顶状态是否有变化
            clearTopRelatedCaches($id, $category_id, $old_top_status, $new_top_status);
        } elseif ($id == 0 && function_exists('clearTopRelatedCaches')) {
            // 新建信息时，如果设置了置顶，清理相关缓存
            $has_top = $new_top_status['home'] || $new_top_status['category'] || $new_top_status['subcategory'];
            if ($has_top) {
                clearTopRelatedCaches($id, $category_id, null, $new_top_status);
            }
        }

        // 返回成功结果
        return array(
            'success' => true,
            'id' => $id,
            'message' => $id > 0 ? '信息更新成功' : '信息添加成功'
        );
        
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        
        // 返回错误信息
        return array(
            'success' => false,
            'error' => $e->getMessage()
        );
    }
}

/**
 * 删除信息
 */
function delete_post($id) {
    global $db;
    
    // 获取信息分类ID
    $category_id = 0;
    $post = get_post_detail($id);
    if ($post && isset($post['category_id'])) {
        $category_id = $post['category_id'];
    }
    
    // 使用事务确保同时删除两个表的数据
    $db->beginTransaction();
    
    try {
        // 首先删除post_contents表中的记录
        $sql = "DELETE FROM post_contents WHERE post_id = ?";
        $db->query($sql, array($id));
        
        // 删除posts表中的记录
        $sql = "DELETE FROM posts WHERE id = ?";
        $result = $db->query($sql, array($id));
        
        // 删除附件
        if (function_exists('deletePostAttachments')) {
            deletePostAttachments($id);
        }
        
        // 提交事务
        $db->commit();

        // 清理相关缓存（删除信息，不需要清理首页缓存）
        clearPostRelatedCaches($id, $category_id, 'delete');

        return true;
    } catch (Exception $e) {
        // 回滚事务
        $db->rollback();
        error_log("删除信息失败: " . $e->getMessage());
        return false;
    }
}

/**
 * 更新信息状态
 */
function update_post_status($id, $status) {
    global $db;
    
    // 获取信息分类ID
    $category_id = 0;
    $post = get_post_detail($id);
    if ($post && isset($post['category_id'])) {
        $category_id = $post['category_id'];
    }
    
    $sql = "UPDATE posts SET status = ?, updated_at = ? WHERE id = ?";
    $result = $db->query($sql, array($status, time(), $id));
    
    if ($result) {
        return true;
    }
    
    return false;
}

/**
 * 获取分类的父ID
 * @param int $categoryId 分类ID
 * @return int 父分类ID，无父分类则返回0
 */
function admin_getCategoryParentId($categoryId) {
    // 使用全局缓存获取分类的父ID
    foreach ($GLOBALS['cached_categories'] as $category) {
        if ($category['id'] == $categoryId) {
            return isset($category['parent_id']) ? intval($category['parent_id']) : 0;
        }
    }
    return 0;
} 