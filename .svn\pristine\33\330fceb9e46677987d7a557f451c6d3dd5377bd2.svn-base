<?php
// 定义安全常量
define('IN_BTMPS', true);
/**
 * 管理员密码重置工具
 * 注意：此文件仅供紧急情况使用，使用后建议删除或重命名
 */
require_once('../include/common.inc.php');

// 设置安全密钥，使用复杂字符串，正式使用时请修改此值
$security_key = '6kd8s3jh2fDFG34f';

// 默认不显示成功信息
$success_message = '';
$error_message = '';

// 限制IP访问
$allowed_ips = array('127.0.0.1', '::1', '***********');
$client_ip = get_client_ip();

// 检查IP是否允许访问
if (!in_array($client_ip, $allowed_ips)) {
    die('访问被拒绝：您的IP地址 (' . $client_ip . ') 没有权限访问此页面');
}

// 处理密码重置请求
if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    // 验证安全密钥
    $input_key = isset($_POST['security_key']) ? trim($_POST['security_key']) : '';
    
    if ($input_key !== $security_key) {
        $error_message = '安全密钥错误，拒绝访问';
    } else {
        $username = isset($_POST['username']) ? filter($_POST['username']) : '';
        $new_password = isset($_POST['new_password']) ? trim($_POST['new_password']) : '';
        $confirm_password = isset($_POST['confirm_password']) ? trim($_POST['confirm_password']) : '';
        
        // 验证输入
        if (empty($username)) {
            $error_message = '请输入管理员用户名';
        } elseif (empty($new_password)) {
            $error_message = '请输入新密码';
        } elseif (strlen($new_password) < 5) {
            $error_message = '新密码长度必须至少为5个字符';
        } elseif ($new_password !== $confirm_password) {
            $error_message = '两次输入的密码不一致';
        } else {
            // 检查管理员是否存在
            $sql = "SELECT id FROM admins WHERE username = '" . $db->escape($username) . "' LIMIT 1";
            $result = $db->query($sql);
            
            if ($result && $db->num_rows($result) > 0) {
                $admin = $db->fetch_array($result);
                
                // 生成密码哈希
                $password_hash = password_hash($new_password, PASSWORD_DEFAULT);
                
                // 获取当前时间戳
                $current_time = time();
                
                // 更新密码 - 使用时间戳而不是NOW()函数
                $sql = "UPDATE admins SET password = '" . $db->escape($password_hash) . "', 
                        updated_at = {$current_time} WHERE id = " . $admin['id'];
                
                if ($db->query($sql)) {
                    // 记录操作日志
                    $log_message = "管理员 {$username} 的密码已被重置，IP：{$client_ip}，时间：" . date('Y-m-d H:i:s');
                    
                    // 确保日志目录存在
                    if (!is_dir('../data/logs')) {
                        mkdir('../data/logs', 0755, true);
                    }
                    
                    error_log($log_message, 3, '../data/logs/admin_pwd_reset.log');
                    
                    $success_message = '密码重置成功！请使用新密码登录系统。';
                } else {
                    $error_message = '密码重置失败，数据库错误: ' . $db->error();
                }
            } else {
                $error_message = '管理员用户名不存在';
            }
        }
    }
}

// 获取所有管理员用户名列表，方便选择
$admin_usernames = array();
$sql = "SELECT username FROM admins ORDER BY id ASC";
$result = $db->query($sql);
if ($result) {
    while ($row = $db->fetch_array($result)) {
        $admin_usernames[] = $row['username'];
    }
}
?>
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>管理员密码重置工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            line-height: 1.6;
            margin: 0;
            padding: 20px;
            background-color: #f5f5f5;
            color: #333;
        }
        .container {
            max-width: 600px;
            margin: 0 auto;
            background-color: #fff;
            padding: 20px;
            border-radius: 5px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
        }
        h1 {
            color: #d9534f;
            border-bottom: 1px solid #eee;
            padding-bottom: 10px;
            margin-top: 0;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        input[type="text"],
        input[type="password"],
        select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        button {
            background-color: #d9534f;
            color: white;
            padding: 10px 15px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 16px;
        }
        button:hover {
            background-color: #c9302c;
        }
        .warning {
            padding: 10px 15px;
            background-color: #fcf8e3;
            color: #8a6d3b;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #f0ad4e;
        }
        .error {
            padding: 10px 15px;
            background-color: #f2dede;
            color: #a94442;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #d9534f;
        }
        .success {
            padding: 10px 15px;
            background-color: #dff0d8;
            color: #3c763d;
            border-radius: 4px;
            margin-bottom: 20px;
            border-left: 4px solid #5cb85c;
        }
        .login-link {
            text-align: center;
            margin-top: 20px;
        }
        .login-link a {
            color: #337ab7;
            text-decoration: none;
        }
        .login-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>管理员密码重置工具</h1>
        
        <div class="warning">
            <strong>警告：</strong> 此工具可以直接重置管理员密码，仅限于系统管理员在紧急情况下使用。
            使用后建议立即删除此文件或修改安全密钥。
        </div>
        
        <?php if (!empty($error_message)): ?>
            <div class="error">
                <strong>错误：</strong> <?php echo $error_message; ?>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($success_message)): ?>
            <div class="success">
                <strong>成功：</strong> <?php echo $success_message; ?>
            </div>
        <?php endif; ?>
        
        <form method="post" action="">
            <div class="form-group">
                <label for="security_key">安全密钥：</label>
                <input type="password" id="security_key" name="security_key" required>
            </div>
            
            <div class="form-group">
                <label for="username">管理员用户名：</label>
                <?php if (!empty($admin_usernames)): ?>
                <select id="username" name="username" required>
                    <option value="">-- 请选择管理员用户 --</option>
                    <?php foreach ($admin_usernames as $admin_username): ?>
                    <option value="<?php echo htmlspecialchars($admin_username); ?>"><?php echo htmlspecialchars($admin_username); ?></option>
                    <?php endforeach; ?>
                </select>
                <?php else: ?>
                <input type="text" id="username" name="username" required>
                <?php endif; ?>
            </div>
            
            <div class="form-group">
                <label for="new_password">新密码：</label>
                <input type="password" id="new_password" name="new_password" required>
            </div>
            
            <div class="form-group">
                <label for="confirm_password">确认新密码：</label>
                <input type="password" id="confirm_password" name="confirm_password" required>
            </div>
            
            <button type="submit">重置密码</button>
        </form>
        
        <div class="login-link">
            <a href="login.php">返回登录页面</a>
        </div>
    </div>
    
    <script>
    // 简单的密码强度检查
    document.getElementById('new_password').addEventListener('input', function() {
        var password = this.value;
        var strength = 0;
        
        if (password.length >= 8) strength += 1;
        if (password.match(/[a-z]+/)) strength += 1;
        if (password.match(/[A-Z]+/)) strength += 1;
        if (password.match(/[0-9]+/)) strength += 1;
        if (password.match(/[^a-zA-Z0-9]+/)) strength += 1;
        
        var strengthColor = '';
        switch (strength) {
            case 0:
            case 1:
                strengthColor = '#d9534f'; // 红色 - 弱
                break;
            case 2:
            case 3:
                strengthColor = '#f0ad4e'; // 黄色 - 中等
                break;
            case 4:
            case 5:
                strengthColor = '#5cb85c'; // 绿色 - 强
                break;
        }
        
        this.style.borderColor = strengthColor;
    });
    </script>
</body>
</html> 