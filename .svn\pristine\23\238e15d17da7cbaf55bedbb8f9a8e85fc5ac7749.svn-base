<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>操作成功 - <?php echo $site_name ?? ""; ?></title>
    <link rel="stylesheet" href="/template/pc/css/common.css">
    <style>
        .success-container {
   
            background: #fff;
            border-radius: 4px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            padding: 40px 0px;
            text-align: center;
      
        }
        
        .success-icon {
            margin-bottom: 30px;
        }
        
        .success-icon i {
            font-size: 80px;
            color: #52c41a;
        }
        
        .success-title {
            font-size: 28px;
            color: #333;
            margin-bottom: 20px;
            font-weight: 500;
        }
        
        .success-message {
            font-size: 16px;
            color: #666;
            margin-bottom: 30px;
            line-height: 1.6;
        }
        
        .countdown {
            font-size: 14px;
            color: #999;
            margin-bottom: 20px;
        }
        
        .btn-group {
            display: flex;
            justify-content: center;
            gap: 15px;
        }
        
        .btn {
            display: inline-block;
            min-width: 120px;
            padding: 10px 25px;
            font-size: 14px;
            border-radius: 4px;
            cursor: pointer;
            transition: all 0.3s;
            text-decoration: none;
        }
        
        .btn-primary {
            background: #1890ff;
            color: #fff;
            border: 1px solid #1890ff;
        }
        
        .btn-primary:hover {
            background: #40a9ff;
            border-color: #40a9ff;
        }
        
        .btn-default {
            background: #fff;
            color: #666;
            border: 1px solid #d9d9d9;
        }
        
        .btn-default:hover {
            color: #40a9ff;
            border-color: #40a9ff;
        }
    </style>
</head>
<body>
        <!-- 顶部 -->
	<div class="yui-top  yui-1200">
		<div class="yui-top-center">
			<div class="yui-top-left yui-left">
				<a href="https://www.botou.net/">1网站首页</a>
				<a href="#">移动版</a>
				<a href="#">微信公众号</a>
				<a href="#">快速发布</a>
			</div>

			<div class="yui-top-right yui-right yui-text-right">
				<a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">会员中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">我的信息</a></li>
						<li><a href="#">我的收藏</a></li>
						<li><a href="#">账号设置</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">商家中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">商家入驻</a></li>
						<li><a href="#">商家管理</a></li>
						<li><a href="#">营销推广</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">网站导航</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">关于我们</a></li>
						<li><a href="#">联系我们</a></li>
						<li><a href="#">使用帮助</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
        <!-- 页面切换导航 -->
        <!-- <div class="page-switch-nav">
            <div class="yui-1200">
                <a href="index.htm" class="active">首页</a>
                <a href="list.htm">列表页</a>
                <a href="view.htm">详情页</a>
            </div>
        </div> -->
	<!-- header-->
	<div class="yui-header yui-1200">

		<div class="yui-t yui-c-box">
			<div class="yui-logo">
				<a href="https://www.botou.net/"><img src="/template/pc/images/logo.png" alt="泊头生活网" srcset=""></a>
			</div>
			<div class="yui-cimg"></div>
			<!--form select -->
			<div class="yui-form">
				<div class="yui-select">
					<!-- <div class="mod_select">
						<div class="select_box">
							<span class="select_txt">信息</span>
							<span class="select-icon"></span>
							<ul class="option">
								<li>信息</li>
								<li>帖子</li>

							</ul>
						</div>
					</div> -->
					<form action="/search.php" method="get" id="header-search-form">

						<input type="hidden" name="show" value="title" />
						<input type="hidden" name="tempid" value="1" />
						<input type="hidden" name="tbname" value="info">
						<input type="text" name="keyword"  class="import" placeholder="请输入关键字" id="header-search-input">
						<input type="submit" class="btn-search" id="header-search-btn" value="搜   索">
					</form>
				</div>
				<div class="yui-select-bottom-text"></div>
			</div>
			<div class="yui-fabu" style="float:right;">
				<button onClick="location.href='/post.php'"><a href="/post.php" target="_blank">免费发布信息</a></button>
			</div>
			<!-- form end -->
		</div>
	</div>
	<div class="yui-clear"></div>
	<div class="yui-nav mt20  yui-1200">
		<ul>
			<li <?php if(!null !== ($current_page ?? null) || $current_page == 'index'): ?>class='nav-cur'<?php endif; ?>><a href="/">首页</a></li>
			<?php 
			// 直接从数据库获取分类数据
			$categories = getCategories();
			
			// 筛选一级分类并排序
			$topCategories = array();
			foreach ($categories as $cat) {
				if ($cat['parent_id'] == 0 && $cat['status'] == 1) {
					$topCategories[] = $cat;
				}
			}
			
			// 按排序值升序排列
			usort($topCategories, function($a, $b) {
				if ($a['sort_order'] == $b['sort_order']) {
					return $a['id'] - $b['id']; // 如果排序值相同，按ID升序
				}
				return $a['sort_order'] - $b['sort_order']; // 按排序值升序
			});
			
			// 输出导航菜单
			foreach ($topCategories as $cat) {
				echo '<li><a href="/'.$cat['pinyin'].'/">'.$cat['name'].'</a></li>';
			}
			 ?>
			<li <?php if(null !== ($current_page ?? null) && $current_page == 'news'): ?>class='nav-cur'<?php endif; ?>><a href="/news.php">新闻中心</a></li>
		</ul>
	</div>

	<script>
	// Header搜索加载状态管理 - 使用多种方式确保兼容性
	(function() {
		function initHeaderSearch() {
			var headerSearchForm = document.getElementById('header-search-form');
			if (headerSearchForm) {
				headerSearchForm.addEventListener('submit', function(e) {
					var input = document.getElementById('header-search-input');
					var keyword = input ? input.value.trim() : '';

					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			}
		}

		function showHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜索中...';
				searchBtn.disabled = true;
				searchBtn.style.backgroundColor = '#6c757d';
				searchBtn.style.cursor = 'not-allowed';

				// 添加调试信息
				console.log('Header搜索加载状态已激活');
			}
		}

		function hideHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜   索';
				searchBtn.disabled = false;
				searchBtn.style.backgroundColor = '#3092d5';
				searchBtn.style.cursor = 'pointer';
			}
		}

		// 多种初始化方式确保兼容性
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', initHeaderSearch);
		} else {
			initHeaderSearch();
		}

		// 如果有jQuery，也用jQuery方式绑定
		if (typeof $ !== 'undefined') {
			$(document).ready(function() {
				$('#header-search-form').on('submit', function(e) {
					var keyword = $('#header-search-input').val().trim();
					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			});
		}

		// 暴露函数到全局作用域，方便调试
		window.showHeaderSearchLoading = showHeaderSearchLoading;
		window.hideHeaderSearchLoading = hideHeaderSearchLoading;
	})();
	</script>

    
    <div class="success-container yui-1200">
        <div class="success-icon">
            <i class="fas fa-check-circle"></i>
        </div>
        <h1 class="success-title">操作成功</h1>
        <div class="success-message"><?php echo $success_message ?? ""; ?></div>
        
        <?php if(null !== ($redirect_url ?? null) && null !== ($redirect_url ?? null) && !empty($redirect_url)): ?>
        <div class="countdown">页面将在 <span id="second">3</span> 秒后自动跳转</div>
        <div class="btn-group">
            <a href="<?php echo $redirect_url ?? ""; ?>" class="btn btn-primary" id="redirect-btn">立即跳转</a>
            <a href="javascript:history.back();" class="btn btn-default">返回上一页</a>
        </div>
        <?php else: ?>
        <div class="btn-group">
            <a href="/" class="btn btn-primary">返回首页</a>
            <a href="javascript:history.back();" class="btn btn-default">返回上一页</a>
        </div>
        <?php endif; ?>
    </div>

    <div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <p class="footer-nav">
                <a href="https://www.botou.net/" title="泊头生活网">网站首页</a>
                <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank">广告服务</a>
                <a href="https://www.botou.net/aboutus/shenmin.html" target="_blank">法律声明</a>
                <a href="https://www.botou.net/aboutus/about.html" target="_blank">网站介绍</a>
                <a href="https://www.botou.net/aboutus/contactus.html" target="_blank">联系我们</a>
                <a href="https://www.botou.net/aboutus/job.html" target="_blank">招聘信息</a>
            </p>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright">Copyright © 2006-2023 <a href="https://www.botou.net" title="泊头生活网" class="db_link">泊头生活网</a> 版权所有</p>
            <p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow">冀ICP备2023009391号-1</a> <a rel="nofollow" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=13098102000307">冀公网安备 13098102000307号</a></p>
        </div>
    </div>
</div>

    <?php if(null !== ($redirect_url ?? null) && null !== ($redirect_url ?? null) && !empty($redirect_url)): ?>
    <script>
        var second = 113;
        var timer = null;
        
        function countDown() {
            second--;
            if (second <= 0) {
                clearInterval(timer);
                window.location.href = '<?php echo $redirect_url ?? ""; ?>';
                return;
            }
            document.getElementById('second').innerText = second;
        }
        
        timer = setInterval(countDown, 1000);
    </script>
    <?php endif; ?>
</body>
</html> 