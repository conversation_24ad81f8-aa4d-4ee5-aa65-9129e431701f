<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo $page_title; ?> - <?php echo $site_name; ?></title>
    <meta name="keywords" content="<?php echo $site_name; ?>,新闻中心,新闻资讯,本地新闻" />
    <meta name="description" content="<?php echo $site_name; ?>新闻中心，提供本地最新新闻资讯。" />
    <link rel="stylesheet" href="/template/pc/css/common.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/pc/css/news.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/5.15.4/css/all.min.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
</head>
<body>
    {include file="header.htm"}

    <div class="yui-clear"></div>
 
    <!-- 面包屑导航 -->
    <div class="yui-1200">
        <div class="breadcrumb-container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span class="separator">></span>
                <span>{$page_title}</span>
            </div>
        </div>
    </div>

        
    <div class="yui-content yui-1200">
        <div class="news-container">
            <!-- 左侧分类导航 -->
            <div class="news-sidebar">
                <div class="category-nav">
                    <ul class="category-list">
                        <li class="category-item">
                            <a href="news.php?op=custom">
                                <span class="icon"></span>
                                <span>站长自定义</span>
                                <span class="new-tag">NEW</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php">
                                <span class="icon"></span>
                                <span>热门</span>
                            </a>
                        </li>
                        <li class="category-item active">
                            <a href="news.php?op=list">
                                <span class="icon"></span>
                                <span>综合</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=1">
                                <span class="icon"></span>
                                <span>互动</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=2">
                                <span class="icon"></span>
                                <span>图文</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=3">
                                <span class="icon"></span>
                                <span>摄影</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=4">
                                <span class="icon"></span>
                                <span>插画</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=5">
                                <span class="icon"></span>
                                <span>新闻</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=6">
                                <span class="icon"></span>
                                <span>杂构</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=7">
                                <span class="icon"></span>
                                <span>产品</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=8">
                                <span class="icon"></span>
                                <span>软件</span>
                            </a>
                        </li>
                        <li class="category-item">
                            <a href="news.php?catid=9">
                                <span class="icon"></span>
                                <span>问答</span>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- 中间主体区域 -->
            <div class="news-main">
                <!-- 新闻焦点 -->
                <div class="news-focus">
                    <div class="news-title">
                        <h3>新闻焦点</h3>
                    </div>
                    <div class="focus-content">
                        <?php
                        if (isset($recommend_news) && !empty($recommend_news) && is_array($recommend_news)) {
                            // 焦点图
                            echo '<div class="focus-main">';
                            echo '<a href="news.php?id=' . $recommend_news[0]['id'] . '">';
                            $thumb = empty($recommend_news[0]['thumb']) ? '/template/pc/images/no_image.jpg' : $recommend_news[0]['thumb'];
                            echo '<img src="' . $thumb . '" alt="' . $recommend_news[0]['title'] . '">';
                            echo '<div class="focus-main-title">';
                            echo '<h3>' . $recommend_news[0]['title'] . '</h3>';
                            echo '</div>';
                            echo '</a>';
                            echo '</div>';
                            
                            // 焦点列表
                            echo '<div class="focus-list">';
                            foreach ($recommend_news as $key => $news) {
                                if ($key > 0 && $key < 5) {
                                    echo '<div class="focus-item">';
                                    echo '<a href="news.php?id=' . $news['id'] . '">' . $news['title'] . '</a>';
                                    if (!empty($news['is_top'])) echo '<span class="top-tag">置顶</span>';
                                    if (!empty($news['is_recommend'])) echo '<span class="rec-tag">推荐</span>';
                                    echo '<div class="item-meta">';
                                    echo '<span>' . date('Y-m-d', $news['addtime']) . '</span>';
                                    if (!empty($news['author'])) {
                                        echo '<span>' . $news['author'] . '</span>';
                                    }
                                    echo '</div>';
                                    echo '</div>';
                                }
                            }
                            echo '</div>';
                        }
                        ?>
                    </div>
                </div>
                
                <!-- 栏目新闻 -->
                <?php if($top_categories): foreach($top_categories as $cat_id => $cat): ?>
                <div class="news-block">
                    <div class="news-title">
                        <h3><?php echo $cat['catname']; ?></h3>
                        <a href="news.php?catid=<?php echo $cat_id; ?>" class="more">更多</a>
                    </div>
                    <div class="news-list">
                        <?php if($cat_news[$cat_id]): foreach($cat_news[$cat_id] as $key => $news): ?>
                        <?php if($key == 0): ?>
                        <!-- 头条新闻 -->
                        <div class="headline-news">
                            <?php if(!empty($news['thumb'])): ?>
                            <div class="headline-img">
                                <a href="news.php?id=<?php echo $news['id']; ?>">
                                    <img src="<?php echo $news['thumb']; ?>" alt="<?php echo $news['title']; ?>">
                                </a>
                            </div>
                            <?php endif; ?>
                            <div class="headline-info">
                                <h3><a href="news.php?id=<?php echo $news['id']; ?>"><?php echo $news['title']; ?></a>
                                <?php if(!empty($news['is_top'])): ?><span class="top-tag">置顶</span><?php endif; ?>
                                <?php if(!empty($news['is_recommend'])): ?><span class="rec-tag">推荐</span><?php endif; ?>
                                </h3>
                                <div class="item-meta">
                                    <span><?php echo date('Y-m-d', $news['addtime']); ?></span>
                                    <span><?php echo $news['catname']; ?></span>
                                </div>
                                <div class="headline-desc">
                                    <?php 
                                    if (!empty($news['description'])) {
                                        echo cut_str($news['description'], 100);
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <!-- 列表新闻 -->
                        <div class="news-item">
                            <h3><a href="news.php?id=<?php echo $news['id']; ?>"><?php echo $news['title']; ?></a>
                            <?php if(!empty($news['is_top'])): ?><span class="top-tag">置顶</span><?php endif; ?>
                            <?php if(!empty($news['is_recommend'])): ?><span class="rec-tag">推荐</span><?php endif; ?>
                            </h3>
                            <div class="item-meta">
                                <span><?php echo date('Y-m-d', $news['addtime']); ?></span>
                                <?php if(!empty($news['author'])): ?>
                                <span><?php echo $news['author']; ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php endforeach; endif; ?>
                    </div>
                </div>
                <?php endforeach; endif; ?>
            </div>
            
            <!-- 右侧边栏 -->
            {include file="news_right.htm"}
        </div>
    </div>
</div>
    {include file="footer.htm"}

    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
    <script type="text/javascript" src="/template/pc/js/common.js"></script>
</body>
</html> 