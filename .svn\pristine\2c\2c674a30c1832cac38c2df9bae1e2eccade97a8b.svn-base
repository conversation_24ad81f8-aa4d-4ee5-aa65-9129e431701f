<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{$page_title} - {$site_name}</title>
    <meta name="keywords" content="{$site_name},{$page_title},新闻资讯,本地新闻" />
    <meta name="description" content="{$site_name}{$page_title}，提供最新{$page_title}相关资讯。" />
    <link rel="stylesheet" href="/template/pc/css/common.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/pc/css/news.css?<?php echo time(); ?>">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
</head>
<body>
    {include file="header.htm"}

    <div class="yui-clear"></div>
    <!-- 面包屑导航 -->
    <div class="yui-1200">
        <div class="breadcrumb-container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span class="separator">></span>
                <span>{$page_title}</span>
            </div>
        </div>
    </div>

    <!-- 主内容区域 -->
    <div class="yui-content yui-1200">
        <div class="news-container">
            <!-- 左侧新闻列表 -->
            <div class="news-left">
                <!-- 筛选按钮 -->
                <div class="news-filter">
                    <div class="filter-buttons">
                        <a href="news.php?op=list{if $catid > 0}&catid={$catid}{/if}" class="filter-btn {if $filter_recommend == 0 && $filter_top == 0}active{/if}">全部</a>
                        <a href="news.php?op=list{if $catid > 0}&catid={$catid}{/if}&recommend=1" class="filter-btn {if $filter_recommend == 1}active{/if}">推荐</a>
                        <a href="news.php?op=list{if $catid > 0}&catid={$catid}{/if}&top=1" class="filter-btn {if $filter_top == 1}active{/if}">置顶</a>
                    </div>
                </div>

                <div class="news-list">
                    {if $news_list}
                    {loop $news_list $news}
                    <!-- 图文新闻 -->
                    {if isset($news.thumb) && $news.thumb}
                    <div class="news-item news-item-img">
                        <div class="news-img">
                            <a href="news.php?id={$news.id}"><img src="{$news.thumb}" alt="{$news.title}"></a>
                        </div>
                        <div class="news-content">
                            <h3>
                                <a href="news.php?id={$news.id}">{$news.title}</a>
                                {if $news.is_top}<span class="top-tag">置顶</span>{/if}
                                {if $news.is_recommend}<span class="rec-tag">推荐</span>{/if}
                            </h3>
                            <div class="meta">
                                <span>发布时间：<?php echo date('Y-m-d', $news['addtime']); ?></span>
                                {if $news.author}<span>来源：{$news.author}</span>{/if}
                                <span>浏览：{$news.click}</span>
                            </div>
                            <div class="desc">
                                <?php 
                                if (!empty($news['description'])) {
                                    echo cut_str($news['description'], 150);
                                }
                                ?>
                            </div>
                        </div>
                    </div>
                    {else}
                    <!-- 纯文字新闻 -->
                    <div class="news-item news-item-text">
                        <h3>
                            <a href="news.php?id={$news.id}">{$news.title}</a>
                            {if $news.is_top}<span class="top-tag">置顶</span>{/if}
                            {if $news.is_recommend}<span class="rec-tag">推荐</span>{/if}
                        </h3>
                        <div class="meta">
                            <span>发布时间：<?php echo date('Y-m-d', $news['addtime']); ?></span>
                            {if $news.author}<span>来源：{$news.author}</span>{/if}
                            <span>浏览：{$news.click}</span>
                        </div>
                        <div class="desc">
                            <?php 
                            if (!empty($news['description'])) {
                                echo cut_str($news['description'], 150);
                            }
                            ?>
                        </div>
                    </div>
                    {/if}
                    {/loop}
                    {else}
                    <div class="empty-list" style="padding: 50px 0; text-align: center; color: #999;">
                        <p>暂无相关新闻</p>
                    </div>
                    {/if}
                </div>
                
                <!-- 分页 -->
                {if $pagebar}
                {$pagebar}
                {/if}
            </div>

            <!-- 右侧边栏 -->
            {include file="news_right.htm"}
        </div>
    </div>
    
    {include file="footer.htm"}

    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
    <script type="text/javascript" src="/template/pc/js/common.js"></script>
</body>
</html> 