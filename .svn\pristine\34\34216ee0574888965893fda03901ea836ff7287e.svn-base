# 缓存清理功能实现说明

## 功能概述

实现了信息修改、刷新、删除时自动更新相关缓存的功能，确保当信息发生变化时，所有相关的缓存都能及时清理，保证数据的一致性和实时性。

## 涉及的缓存类型

### 1. 信息详情缓存
- **键名格式**: `detail_{post_id}_{template_type}`
- **模板类型**: pc, m, wx, app
- **说明**: 存储信息的详细内容，包括标题、内容、联系方式等
- **兼容格式**: `post_detail_{post_id}`, `detail_{post_id}`, `post_{post_id}`

### 2. 首页缓存
- **键名格式**: `index_page_{template_type}`
- **模板类型**: pc, m, wx, app
- **说明**: 存储首页的置顶信息、最新信息等数据

### 3. 分类相关缓存
- **分类列表**: `category_posts_{category_id}_*`
- **分类统计**: `category_posts_count_{category_id}_*`
- **分类信息**: `category_info_{category_id}`
- **分类字段**: `category_fields_{category_id}`
- **分类统计**: `category_stats_{category_id}`

### 4. 相关信息缓存
- **相关信息**: `related_posts_{category_id}_*`
- **相似信息**: `similar_posts_{category_id}_*`
- **热门信息**: `category_hot_posts_{category_id}_*`

### 5. 搜索结果缓存
- **搜索结果**: `search_results_*`
- **搜索统计**: `search_count_*`
- **热门关键词**: `hot_keywords_*`

## 实现的功能函数

### 1. 核心缓存清理函数

#### `clearPostDetailCache($post_id)`
清理信息详情缓存（所有端）
```php
function clearPostDetailCache($post_id) {
    $template_types = array('pc', 'm', 'wx', 'app');
    foreach ($template_types as $template_type) {
        $cache_key = "post_detail_{$post_id}_{$template_type}";
        cache_delete($cache_key);
    }
    // 兼容旧版本
    cache_delete("post_detail_{$post_id}");
}
```

#### `clearHomepageCache()`
清理首页缓存（所有端）
```php
function clearHomepageCache() {
    $template_types = array('pc', 'm', 'wx', 'app');
    foreach ($template_types as $template_type) {
        cache_delete("index_page_{$template_type}");
    }
    // 清理其他首页相关缓存
    clearCacheByPattern('index_*');
    clearCacheByPattern('homepage_*');
    clearCacheByPattern('top_posts_*');
    clearCacheByPattern('latest_posts_*');
    clearCacheByPattern('featured_posts_*');
}
```

#### `clearCategoryRelatedCache($category_id)`
清理分类相关的所有缓存
```php
function clearCategoryRelatedCache($category_id) {
    clearCacheByPattern("category_posts_{$category_id}_*");
    clearCacheByPattern("category_posts_count_{$category_id}_*");
    cache_delete("category_info_{$category_id}");
    cache_delete("category_fields_{$category_id}");
    cache_delete("category_stats_{$category_id}");
}
```

#### `clearRelatedPostsCache($category_id)`
清理相关信息缓存
```php
function clearRelatedPostsCache($category_id) {
    clearCacheByPattern("related_posts_{$category_id}_*");
    clearCacheByPattern("similar_posts_{$category_id}_*");
    clearCacheByPattern("category_hot_posts_{$category_id}_*");
}
```

#### `clearSearchResultCache()`
清理搜索结果缓存
```php
function clearSearchResultCache() {
    clearCacheByPattern('search_results_*');
    clearCacheByPattern('search_count_*');
    clearCacheByPattern('hot_keywords_*');
}
```

### 2. 精确缓存清理函数

#### `clearPostRelatedCaches($post_id, $category_id, $operation)`
精确清理信息相关缓存
```php
function clearPostRelatedCaches($post_id, $category_id = null, $operation = 'update') {
    // 1. 清理信息详情缓存（所有端）
    clearPostDetailCache($post_id);

    // 2. 清理当前分类和父分类的缓存
    if ($category_id) {
        clearCategoryRelatedCache($category_id);
        $category_info = getCategoryInfo($category_id);
        if ($category_info && $category_info['parent_id'] > 0) {
            clearCategoryRelatedCache($category_info['parent_id']);
        }
        clearRelatedPostsCache($category_id);
    }

    // 3. 根据操作类型决定是否清理首页缓存
    if ($operation === 'create' || $operation === 'refresh') {
        clearHomepageCache();
    }

    // 4. 不清理搜索缓存，让其自然过期
}
```

#### `clearAllRelatedCaches($post_id, $category_id, $clear_homepage)` (已废弃)
兼容旧版本的全面缓存清理函数，建议使用 `clearPostRelatedCaches()` 替代

### 3. 辅助函数

#### `clearCacheByPattern($pattern)`
按模式清理缓存文件
```php
function clearCacheByPattern($pattern) {
    $cacheDir = $GLOBALS['file_cache']->getCacheDir();
    $file_pattern = str_replace('*', '*', $pattern);
    $files = glob($cacheDir . $file_pattern . '.cache');
    
    $count = 0;
    foreach ($files as $file) {
        if (@unlink($file)) {
            $count++;
        }
    }
    return $count;
}
```

## 调用位置

### 1. 前台用户操作

#### 新信息发布 (`post.php`)
```php
// 在信息保存成功后（需要清理首页缓存）
clearPostRelatedCaches($postId, $category['id'], 'create');
```

#### 信息修改 (`post.php`)
```php
// 在信息更新成功后（不需要清理首页缓存）
clearPostRelatedCaches($id, $post['category_id'], 'update');
```

#### 信息删除 (`manage.php`)
```php
// 在信息删除成功后（不需要清理首页缓存）
clearPostRelatedCaches($id, $post['category_id'], 'delete');
```

#### 信息刷新 (`manage.php`)
```php
// 在信息刷新成功后（需要清理首页缓存，因为更新时间影响排序）
clearPostRelatedCaches($id, $post['category_id'], 'refresh');
```

### 2. 后台管理操作

#### 新信息创建 (`admin/info.php`)
```php
// 在信息创建成功后（需要清理首页缓存）
clearPostRelatedCaches($new_id, $category_id, 'create');
```

#### 信息编辑 (`admin/info.php`)
```php
// 在信息更新成功后（不需要清理首页缓存）
clearPostRelatedCaches($id, $category_id, 'update');
```

#### 信息删除 (`admin/info.php`)
```php
// 在信息删除成功后（不需要清理首页缓存）
clearPostRelatedCaches($id, $category_id, 'delete');
```

## 精确缓存清理策略

### 1. 清理范围（精确控制）
- **当前信息详情**: 清理该信息的所有详情缓存（PC、手机、微信、APP四个端）
- **当前分类**: 清理该分类的列表、统计等缓存
- **父分类**: 如果存在父分类，也清理父分类的相关缓存
- **相关信息**: 清理同分类下的相关信息缓存
- **首页**: 仅在必要时清理（新建信息、刷新信息）
- **搜索结果**: 不清理，让其自然过期

### 2. 按操作类型的清理策略
- **信息发布(create)**: 清理详情+分类+首页缓存（新信息需要在首页显示）
- **信息修改(update)**: 清理详情+分类缓存（不影响首页排序）
- **信息删除(delete)**: 清理详情+分类缓存（删除不影响首页排序）
- **信息刷新(refresh)**: 清理详情+分类+首页缓存（更新时间影响首页排序）

### 3. 性能优化
- **精确清理**: 只清理真正受影响的缓存
- **避免过度清理**: 不清理搜索缓存，让其自然过期
- **模式匹配**: 使用文件名模式批量清理相关文件
- **详细日志**: 记录清理操作便于调试和监控

## 日志记录

所有缓存清理操作都会记录详细的日志：

```php
error_log("[Cache] 全面清理信息相关缓存完成 - post_id: {$post_id}, category_id: {$category_id} - " . date('Y-m-d H:i:s'));
```

日志包含：
- 操作类型
- 相关ID信息
- 操作时间
- 清理的缓存数量

## 兼容性处理

### 1. 向后兼容
- 保留对旧版本缓存键名的清理支持
- 支持不带模板后缀的缓存键名

### 2. 错误处理
- 使用`@unlink()`避免文件不存在时的错误
- 使用try-catch捕获异常情况
- 提供详细的错误日志

## 使用效果

### 1. 数据一致性
- 确保前台显示的数据与数据库保持一致
- 避免缓存导致的数据延迟问题

### 2. 用户体验
- 信息修改后立即在前台生效
- 新发布的信息立即出现在相关列表中
- 删除的信息立即从前台消失

### 3. 系统性能
- 避免无效缓存占用存储空间
- 确保缓存的有效性和时效性
- 提高缓存命中率

## 注意事项

1. **缓存键名一致性**: 确保清理的缓存键名与生成时的键名完全一致
2. **清理范围**: 根据业务需求选择合适的清理范围，避免过度清理
3. **性能影响**: 缓存清理后需要重新生成，可能短暂影响性能
4. **日志监控**: 定期检查缓存清理日志，确保功能正常工作

## 完成状态

✅ 缓存清理函数实现完成  
✅ 前台操作集成完成  
✅ 后台管理集成完成  
✅ 日志记录功能完成  
✅ 兼容性处理完成  
✅ 文档说明完成  

所有信息的修改、刷新、删除操作现在都会自动更新相关的缓存，确保数据的实时性和一致性。
