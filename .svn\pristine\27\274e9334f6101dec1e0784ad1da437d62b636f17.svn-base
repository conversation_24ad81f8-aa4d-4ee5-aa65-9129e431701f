<?php
/**
 * 举报API接口
 */

// 定义安全常量
define('IN_BTMPS', true);

// 引入公共文件
require_once '../include/common.inc.php';

// 设置响应头
header('Content-Type: application/json; charset=utf-8');

// 只允许POST请求
if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
    http_response_code(405);
    echo json_encode(['success' => false, 'message' => '只允许POST请求']);
    exit;
}

// 获取JSON数据
$input = file_get_contents('php://input');
$data = json_decode($input, true);

if (!$data) {
    echo json_encode(['success' => false, 'message' => '无效的请求数据']);
    exit;
}

// 获取参数
$post_id = isset($data['post_id']) ? intval($data['post_id']) : 0;
$report_type = isset($data['report_type']) ? intval($data['report_type']) : 0;
$content = isset($data['content']) ? trim($data['content']) : '';

// 验证参数
if ($post_id <= 0) {
    echo json_encode(['success' => false, 'message' => '无效的信息ID']);
    exit;
}

if ($report_type <= 0 || $report_type > 4) {
    echo json_encode(['success' => false, 'message' => '请选择有效的举报类型']);
    exit;
}

// 检查信息是否存在
$post = getPostDetail($post_id);
if (!$post) {
    echo json_encode(['success' => false, 'message' => '信息不存在或已被删除']);
    exit;
}

// 举报类型映射
$report_types = array(
    1 => '虚假信息',
    2 => '诈骗信息',
    3 => '违法违规',
    4 => '其他问题'
);

$type_text = $report_types[$report_type];

// 如果没有填写内容，使用默认描述
if (empty($content)) {
    $content = '用户举报：' . $type_text;
}

// 保存举报信息
try {
    $ip = get_client_ip();
    $created_at = time();

    $sql = "INSERT INTO reports (post_id, type, content, ip, created_at, status)
            VALUES (?, ?, ?, ?, ?, 0)";

    $result = $db->query($sql, [$post_id, $type_text, $content, $ip, $created_at]);

    if ($result) {
        echo json_encode(['success' => true, 'message' => '举报提交成功']);
    } else {
        echo json_encode(['success' => false, 'message' => '举报提交失败，请稍后重试']);
    }
} catch (Exception $e) {
    error_log('举报提交失败: ' . $e->getMessage());
    echo json_encode(['success' => false, 'message' => '系统错误，请稍后重试']);
}
?>
