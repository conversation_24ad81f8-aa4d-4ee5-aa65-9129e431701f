<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 分类信息网站后台管理</title>
    <link href="https://cdn.bootcdn.net/ajax/libs/font-awesome/6.4.0/css/all.min.css" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        :root {
            --primary-color: #1b68ff;
            --primary-hover: #0045ce;
            --success-color: #3ad29f;
            --warning-color: #eea303;
            --danger-color: #f82f58;
            --info-color: #17a2b8;
            --text-color: #001a4e;
            --text-secondary: #6c757d;
            --border-color: #e9ecef;
            --bg-gray: #f8f9fa;
            --card-shadow: 0 0.5rem 1rem rgba(18, 38, 63, 0.05);
            --transition: all 0.3s ease;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", <PERSON><PERSON>, "Helvetica Neue", <PERSON><PERSON>, sans-serif;
            font-size: 14px;
            line-height: 1.5;
            color: var(--text-color);
            background: var(--bg-gray);
            display: flex;
            min-height: 100vh;
            align-items: center;
            justify-content: center;
            background: linear-gradient(to right, #4e54c8, #8f94fb);
        }

        .login-container {
            width: 360px;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }

        .login-header {
            padding: 20px 30px;
            text-align: center;
            border-bottom: 1px solid var(--border-color);
        }

        .login-header h1 {
            font-size: 22px;
            color: var(--text-color);
            margin-bottom: 4px;
        }

        .login-header p {
            color: var(--text-secondary);
        }

        .login-body {
            padding: 20px 30px;
        }

        .form-group {
            margin-bottom: 15px;
        }

        .form-label {
            display: block;
            margin-bottom: 6px;
            color: var(--text-color);
            font-weight: 500;
        }

        .form-control {
            width: 100%;
            padding: 8px 12px;
            border: 1px solid var(--border-color);
            border-radius: 4px;
            font-size: 14px;
            transition: var(--transition);
            background-color: #fff;
            color: var(--text-color);
            height: 36px;
        }

        .form-control:focus {
            outline: none;
            border-color: var(--primary-color);
            box-shadow: 0 0 0 3px rgba(27, 104, 255, 0.25);
        }

        .input-group {
            display: flex;
            align-items: stretch;
        }

        .input-group .form-control {
            flex: 1;
            border-top-right-radius: 0;
            border-bottom-right-radius: 0;
        }

        .input-group-append {
            margin-left: -1px;
            width: 120px;
            height: 36px;
        }

        .input-group-text {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 0;
            font-size: 14px;
            font-weight: 400;
            color: var(--text-secondary);
            text-align: center;
            white-space: nowrap;
            background-color: #fff;
            border: 1px solid var(--border-color);
            border-left: 0;
            border-radius: 0 4px 4px 0;
            cursor: pointer;
            height: 100%;
            width: 100%;
            overflow: hidden;
        }

        .input-group-text img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }

        .btn {
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            padding: 8px 20px;
            border-radius: 4px;
            font-size: 15px;
            font-weight: 500;
            cursor: pointer;
            transition: var(--transition);
            border: none;
            outline: none;
            width: 100%;
            height: 36px;
        }

        .btn-primary {
            background: var(--primary-color);
            color: #fff;
        }

        .btn-primary:hover {
            background: var(--primary-hover);
        }

        .alert {
            padding: 12px 16px;
            border-radius: 4px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 12px;
            border: 1px solid transparent;
        }

        .alert i {
            font-size: 18px;
        }

        .alert-danger {
            background: rgba(248, 47, 88, 0.1);
            color: var(--danger-color);
            border-color: rgba(248, 47, 88, 0.2);
        }

        .login-footer {
            padding: 15px 30px;
            text-align: center;
            border-top: 1px solid var(--border-color);
            background: var(--bg-gray);
        }

        .login-footer a {
            color: var(--primary-color);
            text-decoration: none;
        }

        .login-footer a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <h1>管理员登录</h1>
        </div>
        
        <div class="login-body">
            {if isset($login_error)}
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i>
                <span>{$login_error}</span>
            </div>
            {/if}
            
            <form action="login.php" method="post">
                <div class="form-group">
                    <label for="username" class="form-label">用户名</label>
                    <input type="text" class="form-control" id="username" name="username" placeholder="请输入用户名" required>
                </div>
                
                <div class="form-group">
                    <label for="password" class="form-label">密码</label>
                    <input type="password" class="form-control" id="password" name="password" placeholder="请输入密码" required>
                </div>
                
                <div class="form-group">
                    <label for="verify_code" class="form-label">验证码</label>
                    <div class="input-group">
                        <input type="text" class="form-control" id="verify_code" name="verify_code" placeholder="请输入验证码" required autocomplete="off">
                        <div class="input-group-append">
                            <span class="input-group-text">
                                <img src="../captcha.php" alt="验证码" id="verify_img" title="点击刷新验证码">
                            </span>
                        </div>
                    </div>
                    <div class="form-text" style="font-size: 12px; color: #6c757d; margin-top: 4px;">点击图片可刷新验证码</div>
                </div>
                
                <div class="form-group">
                    <button type="submit" class="btn btn-primary">
                        <i class="fas fa-sign-in-alt"></i> 登录
                    </button>
                </div>
            </form>
        </div>
    </div>
    
    <script>
        // 点击验证码刷新
        document.getElementById('verify_img').addEventListener('click', function() {
            this.src = '../captcha.php?t=' + new Date().getTime();
        });
    </script>
</body>
</html> 