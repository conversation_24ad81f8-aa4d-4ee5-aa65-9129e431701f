<?php
/**
 * 全局通用函数库
 * 包含网站各处可能用到的通用函数
 * 
 * 注意：以下函数已移至info.fun.php，这里保留是为了向后兼容：
 * - getCategoryInfo()
 * - getSubCategories()
 * - getCategoryIdByPinyin()
 * - buildCategoryUrl()
 * - getCategoryIntegratedData()
 * - getCategoryPostsDirectly()
 * - getCategoryPostsCount()
 * - getRelatedPosts()
 * - updateViewCount()
 * - getPostDetail()
 * - getRemainingDaysInt()
 * - formatRelativeTime()
 * - friendlyRemainingDays()
 * - getCategoryData()
 * - getCategoryIcon()
 * - getTopPosts()
 * - getNormalPosts()
 * - processAreaSelection()
 * - buildAreaNavigation()
 * - include_404_page()
 */


if (!defined('IN_BTMPS')) {
    exit('Access Denied');
}

/**
 * 获取分类列表（仅启用的分类，用于前台显示）
 */
function getCategories() {
    global $db;

    $sql = "SELECT id, parent_id, name, icon, sort_order, status, pinyin,
            seo_title, seo_keywords, seo_description, template, detail_template,
            subcategory_ids FROM categories WHERE status = 1 ORDER BY sort_order DESC";
    $result = $db->query($sql);

    $categories = array();
    while ($row = $db->fetch_array($result)) {
        // 确保基本字段存在
        if (!isset($row['id'])) continue; // 跳过没有ID的分类

        // 确保parent_id字段存在
        if (!isset($row['parent_id'])) {
            $row['parent_id'] = 0;
        }

        // 确保name字段存在
        if (!isset($row['name'])) {
            $row['name'] = '未命名分类-' . $row['id'];
        }

        // 确保pinyin字段存在
        if (!isset($row['pinyin'])) {
            $row['pinyin'] = 'category-' . $row['id'];
        }

        $categories[] = $row;
    }

    return $categories;
}

/**
 * 获取所有分类列表（包括禁用的分类，用于后台管理）
 */
function getAllCategories() {
    global $db;

    $sql = "SELECT id, parent_id, name, icon, sort_order, status, pinyin,
            seo_title, seo_keywords, seo_description, template, detail_template,
            subcategory_ids FROM categories ORDER BY sort_order DESC";
    $result = $db->query($sql);

    $categories = array();
    while ($row = $db->fetch_array($result)) {
        // 确保基本字段存在
        if (!isset($row['id'])) continue; // 跳过没有ID的分类

        // 确保parent_id字段存在
        if (!isset($row['parent_id'])) {
            $row['parent_id'] = 0;
        }

        // 确保name字段存在
        if (!isset($row['name'])) {
            $row['name'] = '未命名分类-' . $row['id'];
        }

        // 确保pinyin字段存在
        if (!isset($row['pinyin'])) {
            $row['pinyin'] = 'category-' . $row['id'];
        }

        $categories[] = $row;
    }

    return $categories;
}



/**
 * 获取区域列表
 */
function getRegions() {
    global $db;
    
    // 从数据库获取
    $sql = "SELECT id, parent_id, name, level FROM regions WHERE level = 1 ORDER BY sort_order DESC, id ASC";
    $result = $db->query($sql);
    
    $regions = array();
    while ($province = $db->fetch_array($result)) {
        // 为每个省级区域查找其下属的市级区域
        $sql2 = "SELECT id, parent_id, name, level FROM regions WHERE parent_id = {$province['id']} AND level = 2 ORDER BY sort_order DESC, id ASC";
        $result2 = $db->query($sql2);
        
        $cities = array();
        while ($city = $db->fetch_array($result2)) {
            $cities[] = $city;
        }
        
        // 如果没有市级区域，创建一个默认的市级区域
        if (empty($cities)) {
            $cities[] = array(
                'id' => $province['id'] * 100, // 创建一个不会与其他ID冲突的ID
                'parent_id' => $province['id'],
                'name' => $province['name'] . '市',
                'level' => 2
            );
        }
        
        $province['children'] = $cities;
        $regions[] = $province;
    }
    
    return $regions;
}

/**
 * 向模板分配变量
 */
function assign($name, $value = '') {
    global $tpl;
    $tpl->assign($name, $value);
}

/**
 * 显示模板
 * @param string $template 模板文件名
 * @param bool $isMobile 是否为移动设备访问
 */
function display($template, $isMobile = false) {
    global $tpl;
    
    // 根据是否为移动设备设置不同的模板目录
    if ($isMobile) {
        // 移动设备使用m目录
        $tpl->setTemplateDir(ROOT_PATH . 'template/m/');
    } else {
        // 使用TEMPLATE_DIR确定的目录（www, wx, app等）
        $tpl->setTemplateDir(ROOT_PATH . 'template/' . TEMPLATE_DIR . '/');
    }
    
    $tpl->display($template);
}



/**
 * 显示错误信息
 */
function show_error($message) {
    assign('error', $message);
    display('error.htm');
    exit;
}


/**
 * 显示成功信息
 */

function show_success($message, $redirect_url = '') {
        assign('success_message', $message);
        assign('redirect_url', $redirect_url);
        display('success.htm');
        exit;
}

/**
 * 获取当前使用的模板目录
 * 返回当前正在使用的模板目录名称
 * 
 * @return string 当前模板目录名称 (www|m|wx|app)
 */
function getCurrentTemplateDir() {
    global $current_template_dir;
    
    if (isset($current_template_dir)) {
        return $current_template_dir;
    }
    
    // 如果全局变量中没有，则返回常量定义的值
    return defined('TEMPLATE_DIR') ? TEMPLATE_DIR : 'www';
}

/**
 * 切换模板目录
 * 设置强制使用的模板目录，会保存到Cookie中
 * 
 * @param string $template 模板目录名称 (www|m|wx|app)
 * @param int $expire Cookie过期时间（秒），默认1天
 * @return bool 是否成功切换
 */
function switchTemplate($template, $expire = 86400) {
    global $current_template_dir;
    
    // 验证模板名称
    if (!in_array($template, array('www', 'm', 'wx', 'app'))) {
        return false;
    }
    
    // 设置Cookie
    setcookie('force_template', $template, time() + $expire, '/');
    
    // 更新全局变量
    $current_template_dir = $template;
    
    return true;
}

/**
 * 清除模板强制设置
 * 删除Cookie中保存的模板设置，恢复自动检测
 * 
 * @return bool 是否成功清除
 */
function clearTemplateForce() {
    global $current_template_dir;
    
    // 删除Cookie
    setcookie('force_template', '', time() - 3600, '/');
    
    // 恢复自动检测
    $current_template_dir = null;
    
    return true;
}


/**
 * 记录系统日志
 */
function log_message($message, $type = 'info') {
    $logFile = DATA_PATH . 'logs/' . date('Y-m-d') . '.log';
    $time = date('Y-m-d H:i:s');
    $logMessage = "[$time][$type] $message\r\n";
    
    // 确保日志目录存在
    if (!is_dir(dirname($logFile))) {
        mkdir(dirname($logFile), 0777, true);
    }
    
    file_put_contents($logFile, $logMessage, FILE_APPEND);
}


/**
 * URL重定向
 */
function redirect($url) {
    header("Location: $url");
    exit;
}

/**
 * 生成URL
 */
function url($path, $params = array()) {
    $query = empty($params) ? '' : '?' . http_build_query($params);
    return $path . $query;
}

/**
 * 输出JSON数据
 */
function output_json($data) {
    header('Content-Type: application/json');
    echo json_encode($data);
    exit;
}


/**
 * 过滤函数 - 用于安全过滤用户输入
 */
function filter($str, $type = 'string') {
    if (is_array($str)) {
        $result = array();
        foreach ($str as $key => $value) {
            $result[$key] = filter($value, $type);
        }
        return $result;
    }
    
    switch ($type) {
        case 'string':
            return htmlspecialchars(trim($str), ENT_QUOTES);
        case 'int':
            return intval($str);
        case 'float':
            return floatval($str);
        case 'email':
            return filter_var($str, FILTER_VALIDATE_EMAIL);
        case 'url':
            return filter_var($str, FILTER_VALIDATE_URL);
        default:
            return htmlspecialchars(trim($str), ENT_QUOTES);
    }
}


/**
 * 获取地区信息
 */
function getAreaInfo($areaId) {
    global $db;
    
    // 直接从数据库查询
    $sql = "SELECT id, name, parent_id FROM regions WHERE id = $areaId";
    $result = $db->query($sql);
    
    return $db->fetch_array($result);
}

/**
 * 获取分类字段
 */
function getCategoryFields($categoryId) {
    global $db;
    
    $sql = "SELECT * FROM fields WHERE category_id = $categoryId OR category_id = 0 ORDER BY sort_order ASC";
    $result = $db->query($sql);
    
    $fields = array();
    while ($row = $db->fetch_array($result)) {
        $fields[] = $row;
    }
    
    return $fields;
}

/**
 * 验证短信验证码
 */
function verifySmsCode($mobile, $code) {
    global $db;
    
    // 查询验证码记录
    $sql = "SELECT * FROM sms_codes WHERE mobile = ? AND code = ? AND is_used = 0 AND expired_at > ? ORDER BY id DESC LIMIT 1";
    $result = $db->query($sql, [$mobile, $code, time()]);
    $row = $db->fetch_array($result);
    
    if ($row) {
        // 标记验证码为已使用
        $sql = "UPDATE sms_codes SET is_used = 1 WHERE id = ?";
        $db->query($sql, [$row['id']]);
        return true;
    }
    
    return false;
}

/**
 * 生成随机验证码
 */
function generateVerifyCode($length = 6) {
    return rand(pow(10, $length-1), pow(10, $length)-1);
}


/**
 * 获取客户端IP地址
 */
function get_client_ip() {
    if (isset($_SERVER['HTTP_CLIENT_IP'])) {
        $ip = $_SERVER['HTTP_CLIENT_IP'];
    } else if (isset($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        $ip = $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        $ip = $_SERVER['REMOTE_ADDR'];
    }
    return $ip;
}

/**
 * 获取当前页URL
 */
function getCurrentUrl() {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    return $protocol . $_SERVER['HTTP_HOST'] . $_SERVER['REQUEST_URI'];
}

/**
 * 获取网站根URL
 */
function getSiteUrl() {
    $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
    return $protocol . $_SERVER['HTTP_HOST'];
}


function friendlyTime($time) {
	$rtime = date("Y-m-d", $time);
	$htime = date("H:i", $time);
	$time = time() - $time;
	if ($time < 60) {
		$str = '刚刚';
	} elseif ($time < 60 * 60) {
		$min = floor($time / 60);
		$str = '' . $min . '分钟前';
	} elseif ($time < 60 * 60 * 24) {
		$h = floor($time / (60 * 60));
		$str = '' . $h . '小时前';
	} elseif ($time < 60 * 60 * 24 * 3) {
		$d = floor($time / (60 * 60 * 24));
		if ($d == 1) {
			$str = '昨天';
		} else {
			$str = '前天';
		}
	} else {
		$str = $rtime;
	}
	return $str;
}


/**
 * 截取字符串
 */
function subString($string, $length, $append = '...') {
    if (mb_strlen($string, 'UTF-8') <= $length) {
        return $string;
    }
    
    return mb_substr($string, 0, $length, 'UTF-8') . $append;
}

/**
 * 生成CSRF令牌
 * 用于防止跨站请求伪造攻击
 * 
 * @param bool $force_new 是否强制生成新令牌，默认为false
 * @return string 生成的CSRF令牌
 */
function generate_csrf_token($force_new = false) {
    // 如果不强制生成新令牌且已有令牌存在，则使用现有令牌
    if (!$force_new && isset($_SESSION['csrf_token']) && isset($_SESSION['csrf_token_time'])) {
        // 检查令牌是否过期（默认1小时）
        if (time() - $_SESSION['csrf_token_time'] < 3600) {
            return $_SESSION['csrf_token'];
        }
    }
    
    // 需要生成新令牌的情况：强制生成新令牌、无现有令牌或令牌已过期
    $token = bin2hex(random_bytes(32));
    $_SESSION['csrf_token'] = $token;
    $_SESSION['csrf_token_time'] = time();
    
    // 返回会话中的令牌
    return $_SESSION['csrf_token'];
}

/**
 * 验证CSRF令牌
 * 检查提交的令牌是否与会话中的令牌匹配
 * 
 * @param string $token 要验证的令牌
 * @param int $maxAge 令牌的最大有效期（秒），默认3600秒(1小时)
 * @param bool $invalidate_after_check 是否在验证后立即使令牌失效，默认为false
 * @return bool 验证是否成功
 */
function verify_csrf_token($token, $maxAge = 3600, $invalidate_after_check = false) {
    // 如果会话中没有令牌，验证失败
    if (!isset($_SESSION['csrf_token']) || !isset($_SESSION['csrf_token_time'])) {
        return false;
    }
    
    // 检查令牌是否过期
    $tokenTime = $_SESSION['csrf_token_time'];
    if (time() - $tokenTime > $maxAge) {
        // 令牌已过期，清除并返回失败
        unset($_SESSION['csrf_token']);
        unset($_SESSION['csrf_token_time']);
        return false;
    }
    
    // 保存会话中的令牌，用于比较
    $session_token = $_SESSION['csrf_token'];
    
    // 这里不再自动使令牌失效，让令牌可以继续使用
    // 只有在显式调用invalidate_csrf_token()时才会使令牌失效
    
    // 检查令牌是否匹配
    return hash_equals($session_token, $token);
}

/**
 * 使当前CSRF令牌失效
 * 在完成验证并成功处理请求后调用，以防止令牌被重用
 */
function invalidate_csrf_token() {
    unset($_SESSION['csrf_token']);
    unset($_SESSION['csrf_token_time']);
}

/**
 * 刷新CSRF令牌
 * 生成新的令牌，但不立即使旧令牌失效
 * 
 * @return string 新生成的令牌
 */
function refresh_csrf_token() {
    // 生成新的令牌
    $token = bin2hex(random_bytes(32));
    
    // 更新会话中的令牌和时间
    $_SESSION['csrf_token'] = $token;
    $_SESSION['csrf_token_time'] = time();
    
    return $token;
}

/**
 * 获取当前请求的Referer
 * 用于验证请求来源
 * 
 * @return string|null 请求的Referer，如果不存在则返回null
 */
function get_request_referer() {
    return isset($_SERVER['HTTP_REFERER']) ? $_SERVER['HTTP_REFERER'] : null;
}

/**
 * 验证请求Referer是否来自本站
 * 用于防止外部提交
 * 
 * @param string|null $referer 要验证的Referer，如果为null则使用当前请求的Referer
 * @return bool 验证是否成功
 */
function validate_referer($referer = null) {
    // 获取Referer
    $referer = $referer ?: get_request_referer();
    if (!$referer) {
        return false;
    }
    
    // 获取当前站点域名
    $siteUrl = getSiteUrl();
    $parsedSiteUrl = parse_url($siteUrl);
    $siteHost = isset($parsedSiteUrl['host']) ? $parsedSiteUrl['host'] : '';
    
    // 解析Referer
    $parsedReferer = parse_url($referer);
    $refererHost = isset($parsedReferer['host']) ? $parsedReferer['host'] : '';
    
    // 检查Referer主机名是否匹配
    return $refererHost === $siteHost;
}

/**
 * 获取缓存的分类数据 - 使用文件缓存系统
 *
 * @param bool $forceRefresh 是否强制刷新缓存
 * @return array 分类数据数组
 */
function getCachedCategories($forceRefresh = false) {
    global $config;

    // 检查全局缓存设置
    $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
    $cache_data_time = isset($config['cache_data']) ? intval($config['cache_data']) : 3600;

    $cacheKey = 'categories_all';

    // 只有在缓存开启且缓存时间大于0时才尝试读取缓存
    if (!$forceRefresh && $cache_enable && $cache_data_time > 0 && function_exists('cache_get')) {
        $cached = cache_get($cacheKey);
        if ($cached !== false) {
            return $cached;
        }
    }

    // 从数据库获取数据
    $categories = getCategories();

    // 只有在缓存开启且缓存时间大于0时才设置缓存
    if ($cache_enable && $cache_data_time > 0 && function_exists('cache_set')) {
        cache_set($cacheKey, $categories, $cache_data_time);
    }

    return $categories;
}

/**
 * 获取缓存的区域数据 - 使用文件缓存系统
 *
 * @param bool $forceRefresh 是否强制刷新缓存
 * @return array 区域数据数组
 */
function getCachedRegions($forceRefresh = false) {
    global $config;

    // 检查全局缓存设置
    $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
    $cache_data_time = isset($config['cache_data']) ? intval($config['cache_data']) : 3600;

    $cacheKey = 'regions_all';

    // 只有在缓存开启且缓存时间大于0时才尝试读取缓存
    if (!$forceRefresh && $cache_enable && $cache_data_time > 0 && function_exists('cache_get')) {
        $cached = cache_get($cacheKey);
        if ($cached !== false) {
            return $cached;
        }
    }

    // 从数据库获取数据
    $regions = getRegions();

    // 只有在缓存开启且缓存时间大于0时才设置缓存
    if ($cache_enable && $cache_data_time > 0 && function_exists('cache_set')) {
        cache_set($cacheKey, $regions, $cache_data_time);
    }

    return $regions;
}

/**
 * 获取缓存的导航数据 - 使用文件缓存系统
 *
 * @param bool $forceRefresh 是否强制刷新缓存
 * @return array 导航数据数组
 */
function getCachedNavigation($forceRefresh = false) {
    global $config, $db;

    // 检查全局缓存设置
    $cache_enable = isset($config['cache_enable']) ? intval($config['cache_enable']) : 1;
    $cache_data_time = isset($config['cache_data']) ? intval($config['cache_data']) : 3600;

    $cacheKey = 'navigation_all';

    // 只有在缓存开启且缓存时间大于0时才尝试读取缓存
    if (!$forceRefresh && $cache_enable && $cache_data_time > 0 && function_exists('cache_get')) {
        $cached = cache_get($cacheKey);
        if ($cached !== false) {
            return $cached;
        }
    }

    // 从数据库获取导航数据
    $navList = array();
    try {
        $sql = "SELECT id, navname, url, target, navorder FROM navigation WHERE status = 1 ORDER BY navorder ASC, id ASC";
        $result = $db->query($sql);
        if ($result) {
            while ($row = $db->fetch_array($result)) {
                $navList[] = $row;
            }
        }
    } catch (Exception $e) {
        // 如果表不存在或查询出错，使用默认导航
        $navList = array(
            array('id' => 1, 'navname' => '首页', 'url' => '/', 'target' => '_self', 'navorder' => 1),
            array('id' => 2, 'navname' => '发布信息', 'url' => '/post.php', 'target' => '_self', 'navorder' => 2),
            array('id' => 3, 'navname' => '最新资讯', 'url' => '/news.php', 'target' => '_self', 'navorder' => 3)
        );
    }

    // 只有在缓存开启且缓存时间大于0时才设置缓存
    if ($cache_enable && $cache_data_time > 0 && function_exists('cache_set')) {
        cache_set($cacheKey, $navList, $cache_data_time);
    }

    return $navList;
}

/**
 * 构建分类映射表，提高查找效率
 * 一次性构建所有需要的映射关系，避免重复循环
 *
 * @param array $categories 分类数据数组
 * @return array 包含各种映射关系的数组
 */
function buildCategoryMaps($categories) {
    $pinyinToIdMap = [];
    $idToCategoryMap = [];
    $parentToChildrenMap = [];

    // 一次循环构建所有映射关系
    foreach ($categories as $cat) {
        $catId = $cat['id'];
        $parentId = isset($cat['parent_id']) ? $cat['parent_id'] : 0;

        // 构建ID到分类的映射
        $idToCategoryMap[$catId] = $cat;

        // 构建拼音到ID的映射
        if (!empty($cat['pinyin'])) {
            $pinyinToIdMap[$cat['pinyin']] = $catId;
        }

        // 构建父级到子级的映射
        if (!isset($parentToChildrenMap[$parentId])) {
            $parentToChildrenMap[$parentId] = [];
        }
        $parentToChildrenMap[$parentId][] = $cat;
    }

    return [
        'pinyin_to_id' => $pinyinToIdMap,
        'id_to_category' => $idToCategoryMap,
        'parent_to_children' => $parentToChildrenMap
    ];
}

/**
 * 截取文本长度函数
 * 可以截取指定长度的UTF-8字符串
 * 
 * @param string $string 要截取的字符串
 * @param int $length 要截取的长度
 * @param string $suffix 截断后的后缀，默认为...
 * @param bool $stripTags 是否去除HTML标签，默认为true
 * @return string 截取后的字符串
 */
function cut_str($string, $length, $suffix = '...', $stripTags = true) {
    // 如果需要去除HTML标签
    if ($stripTags) {
        $string = strip_tags($string);
    }
    
    // 去除空格和换行
    $string = preg_replace('/\s+/', ' ', $string);
    $string = trim($string);
    
    // 如果字符串长度小于等于截取长度，直接返回
    if (mb_strlen($string, 'UTF-8') <= $length) {
        return $string;
    }
    
    // 截取指定长度的字符串
    $cut = mb_substr($string, 0, $length, 'UTF-8');
    
    // 添加后缀
    return $cut . $suffix;
}
