/**
 * 移动端图片压缩库
 * 针对移动设备优化的轻量级图片压缩
 * 
 * <AUTHOR>
 * @version 1.0.0
 */

class MobileImageCompressor {
    constructor(options = {}) {
        this.config = {
            // 移动端默认配置
            quality: 0.7,
            maxWidth: 1200,
            maxHeight: 1200,
            maxSize: 4 * 1024 * 1024, // 4MB
            targetSize: 1.5 * 1024 * 1024, // 1.5MB
            outputFormat: 'image/jpeg',
            maintainAspectRatio: true,
            qualityStep: 0.15,
            minQuality: 0.4,
            // 移动端特有配置
            enableExifRotation: true,
            touchOptimized: true,
            showProgress: true
        };

        Object.assign(this.config, options);
        this.isCompressing = false;
    }

    /**
     * 压缩单个文件 - 移动端优化版本
     */
    async compressFile(file, progressCallback) {
        if (this.isCompressing) {
            throw new Error('正在压缩其他图片，请稍候...');
        }

        this.isCompressing = true;

        try {
            // 检查文件
            if (!this.isValidImage(file)) {
                throw new Error('请选择有效的图片文件');
            }

            if (file.size > this.config.maxSize) {
                throw new Error(`图片过大，请选择小于${this.formatSize(this.config.maxSize)}的图片`);
            }

            // 如果文件已经很小，直接返回
            if (file.size <= this.config.targetSize) {
                return {
                    file: file,
                    compressed: false,
                    originalSize: file.size,
                    finalSize: file.size
                };
            }

            // 显示进度
            if (progressCallback) progressCallback(10, '正在读取图片...');

            // 读取图片
            const img = await this.loadImage(file);
            
            if (progressCallback) progressCallback(30, '正在分析图片...');

            // 获取EXIF方向信息（移动端拍照常见问题）
            let orientation = 1;
            if (this.config.enableExifRotation) {
                orientation = await this.getImageOrientation(file);
            }

            if (progressCallback) progressCallback(50, '正在压缩图片...');

            // 压缩图片
            const result = this.performCompression(img, file, orientation);

            if (progressCallback) progressCallback(100, '压缩完成');

            return result;

        } finally {
            this.isCompressing = false;
        }
    }

    /**
     * 加载图片
     */
    loadImage(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            
            reader.onload = (e) => {
                const img = new Image();
                
                img.onload = () => resolve(img);
                img.onerror = () => reject(new Error('图片加载失败'));
                img.src = e.target.result;
            };
            
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsDataURL(file);
        });
    }

    /**
     * 执行压缩
     */
    performCompression(img, originalFile, orientation = 1) {
        // 计算新尺寸
        const dimensions = this.calculateDimensions(img.width, img.height, orientation);
        
        // 创建Canvas
        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        // 根据方向设置canvas尺寸
        if (orientation > 4) {
            canvas.width = dimensions.height;
            canvas.height = dimensions.width;
        } else {
            canvas.width = dimensions.width;
            canvas.height = dimensions.height;
        }

        // 设置图片质量
        ctx.imageSmoothingEnabled = true;
        ctx.imageSmoothingQuality = 'high';

        // 应用旋转
        this.applyOrientation(ctx, orientation, canvas.width, canvas.height);

        // 绘制图片
        ctx.drawImage(img, 0, 0, dimensions.width, dimensions.height);

        // 压缩到目标大小
        let quality = this.config.quality;
        let compressedFile;
        let attempts = 0;

        do {
            const dataUrl = canvas.toDataURL(this.config.outputFormat, quality);
            compressedFile = this.dataURLToFile(dataUrl, originalFile.name);
            
            if (compressedFile.size <= this.config.targetSize || quality <= this.config.minQuality) {
                break;
            }
            
            quality -= this.config.qualityStep;
            attempts++;
        } while (attempts < 5); // 移动端限制压缩尝试次数

        return {
            file: compressedFile,
            compressed: true,
            originalSize: originalFile.size,
            finalSize: compressedFile.size,
            compressionRatio: ((1 - compressedFile.size / originalFile.size) * 100).toFixed(1) + '%',
            quality: quality,
            dimensions: dimensions
        };
    }

    /**
     * 计算压缩尺寸
     */
    calculateDimensions(width, height, orientation) {
        let newWidth = width;
        let newHeight = height;

        // 移动端通常需要更激进的尺寸压缩
        const maxWidth = this.config.maxWidth;
        const maxHeight = this.config.maxHeight;

        if (this.config.maintainAspectRatio) {
            const aspectRatio = width / height;
            
            if (width > maxWidth) {
                newWidth = maxWidth;
                newHeight = newWidth / aspectRatio;
            }
            
            if (newHeight > maxHeight) {
                newHeight = maxHeight;
                newWidth = newHeight * aspectRatio;
            }
        } else {
            newWidth = Math.min(width, maxWidth);
            newHeight = Math.min(height, maxHeight);
        }

        return {
            width: Math.round(newWidth),
            height: Math.round(newHeight)
        };
    }

    /**
     * 应用EXIF方向
     */
    applyOrientation(ctx, orientation, width, height) {
        switch (orientation) {
            case 2:
                ctx.transform(-1, 0, 0, 1, width, 0);
                break;
            case 3:
                ctx.transform(-1, 0, 0, -1, width, height);
                break;
            case 4:
                ctx.transform(1, 0, 0, -1, 0, height);
                break;
            case 5:
                ctx.transform(0, 1, 1, 0, 0, 0);
                break;
            case 6:
                ctx.transform(0, 1, -1, 0, height, 0);
                break;
            case 7:
                ctx.transform(0, -1, -1, 0, height, width);
                break;
            case 8:
                ctx.transform(0, -1, 1, 0, 0, width);
                break;
        }
    }

    /**
     * 获取图片方向（简化版）
     */
    async getImageOrientation(file) {
        return new Promise((resolve) => {
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const view = new DataView(e.target.result);
                
                if (view.getUint16(0, false) !== 0xFFD8) {
                    resolve(1);
                    return;
                }

                let offset = 2;
                const length = view.byteLength;

                while (offset < length) {
                    const marker = view.getUint16(offset, false);
                    offset += 2;

                    if (marker === 0xFFE1) {
                        const exifLength = view.getUint16(offset, false);
                        offset += 2;

                        if (view.getUint32(offset, false) === 0x45786966) {
                            // 简化的EXIF解析
                            try {
                                const orientation = view.getUint16(offset + 16, false);
                                resolve(orientation > 0 && orientation <= 8 ? orientation : 1);
                                return;
                            } catch (e) {
                                resolve(1);
                                return;
                            }
                        }
                    } else if (marker === 0xFFDA) {
                        break;
                    } else {
                        offset += view.getUint16(offset, false);
                    }
                }

                resolve(1);
            };

            reader.onerror = () => resolve(1);
            reader.readAsArrayBuffer(file.slice(0, 64 * 1024));
        });
    }

    /**
     * 检查是否为有效图片
     */
    isValidImage(file) {
        const validTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/webp'];
        return file && validTypes.includes(file.type.toLowerCase());
    }

    /**
     * DataURL转File
     */
    dataURLToFile(dataURL, filename) {
        const arr = dataURL.split(',');
        const mime = arr[0].match(/:(.*?);/)[1];
        const bstr = atob(arr[1]);
        let n = bstr.length;
        const u8arr = new Uint8Array(n);
        
        while (n--) {
            u8arr[n] = bstr.charCodeAt(n);
        }
        
        return new File([u8arr], filename, { type: mime });
    }

    /**
     * 格式化文件大小
     */
    formatSize(bytes) {
        if (bytes < 1024) return bytes + ' B';
        if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
        return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
    }

    /**
     * 创建移动端友好的进度提示
     */
    showMobileProgress(container, show = true) {
        if (!container) return;

        const progressId = 'mobile-compress-progress';
        let progressEl = document.getElementById(progressId);

        if (show) {
            if (!progressEl) {
                progressEl = document.createElement('div');
                progressEl.id = progressId;
                progressEl.innerHTML = `
                    <div style="position: fixed; top: 50%; left: 50%; transform: translate(-50%, -50%); 
                                background: rgba(0,0,0,0.8); color: white; padding: 20px; 
                                border-radius: 8px; z-index: 9999; text-align: center;">
                        <div style="margin-bottom: 10px;">正在压缩图片...</div>
                        <div style="width: 200px; height: 4px; background: #333; border-radius: 2px;">
                            <div id="mobile-progress-bar" style="width: 0%; height: 100%; background: #007bff; border-radius: 2px; transition: width 0.3s;"></div>
                        </div>
                    </div>
                `;
                document.body.appendChild(progressEl);
            }
            progressEl.style.display = 'block';
        } else {
            if (progressEl) {
                progressEl.style.display = 'none';
            }
        }

        return progressEl;
    }

    /**
     * 更新移动端进度
     */
    updateMobileProgress(percentage, text) {
        const progressBar = document.getElementById('mobile-progress-bar');
        const progressEl = document.getElementById('mobile-compress-progress');
        
        if (progressBar) {
            progressBar.style.width = percentage + '%';
        }
        
        if (progressEl && text) {
            const textEl = progressEl.querySelector('div div');
            if (textEl) textEl.textContent = text;
        }
    }
}

// 导出
if (typeof window !== 'undefined') {
    window.MobileImageCompressor = MobileImageCompressor;
}
