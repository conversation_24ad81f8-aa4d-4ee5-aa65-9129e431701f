<!-- 配置UEditor -->
<script type="text/javascript">
    // 确保UEditor能够找到自己的根路径
    window.UEDITOR_HOME_URL = "/admin/static/ueditor/";
</script>

<!-- 引入UEditor -->
<script type="text/javascript" src="/admin/static/ueditor/ueditor.config.js"></script>
<script type="text/javascript" src="/admin/static/ueditor/ueditor.all.min.js"></script>

<!-- UEditor初始化脚本 -->
<script type="text/javascript">
    // 页面加载完成后初始化UEditor
    document.addEventListener('DOMContentLoaded', function() {
        var ue = UE.getEditor('editor', {
            initialFrameWidth: '100%',  // 设置编辑器宽度为100%
            initialFrameHeight: 500,    // 增加编辑器高度到500px
            toolbars: [
                ['fullscreen', 'source', '|', 'undo', 'redo', '|',
                'bold', 'italic', 'underline', 'fontborder', 'strikethrough', 'superscript', 'subscript', 'removeformat', 'formatmatch', 'autotypeset', 'blockquote', 'pasteplain', '|', 'forecolor', 'backcolor', 'insertorderedlist', 'insertunorderedlist', 'selectall', 'cleardoc', '|',
                'rowspacingtop', 'rowspacingbottom', 'lineheight', '|',
                'customstyle', 'paragraph', 'fontfamily', 'fontsize', '|',
                'directionalityltr', 'directionalityrtl', 'indent', '|',
                'justifyleft', 'justifycenter', 'justifyright', 'justifyjustify', '|', 
                'link', 'unlink', '|', 'imagenone', 'imageleft', 'imageright', 'imagecenter', '|',
                'simpleupload', 'insertimage', 'emotion', 'insertvideo', '|',
                'horizontal', 'date', 'time', 'spechars', '|',
                'inserttable', 'deletetable', 'insertparagraphbeforetable', 'insertrow', 'deleterow', 'insertcol', 'deletecol', 'mergecells', 'mergeright', 'mergedown', 'splittocells', 'splittorows', 'splittocols', '|',
                'preview']
            ],
            enableAutoSave: true,      // 启用自动保存
            saveInterval: 60000,        // 自动保存间隔，单位为毫秒
            zIndex: 0,                 // 编辑器层级
            autoHeightEnabled: false   // 是否自动高度
        });
    });
</script> 