/* post.css - 信息发布相关样式 */

/* 选择栏目页面样式 */
.top-steps{display:flex;justify-content:center;align-items:center;margin:10px auto;max-width:700px;position:relative;}
.step-item{display:flex;flex-direction:column;align-items:center;position:relative;width:33.33%;}
.step-item:not(:last-child)::after{content:"";position:absolute;height:1px;width:100%;background:#ddd;top:15px;left:50%;z-index:1;}
.step-num{width:30px;height:30px;line-height:30px;text-align:center;background:#ccc;color:#fff;font-weight:bold;transform:rotate(45deg);margin-bottom:8px;position:relative;z-index:2;}
.step-num span{display:inline-block;transform:rotate(-45deg);}
.step-active .step-num{background:#f05654;box-shadow:0 0 0 2px rgba(240,86,84,0.2);}
.step-text{color:#999;font-size:14px;}
.step-active .step-text{color:#f05654;}

/* 分类列表样式 */
.category-list{width:100%;}
.primary-category{margin-bottom:2px;border-bottom:1px solid #f0f0f0;padding:4px 2px;position:relative;background-color:#fff;display:flex;}
.primary-category-title{font-size:16px;font-weight:bold;color:#333;display:flex;align-items:center;width:100px;min-width:100px;margin-right:5px;}
.secondary-categories{display:flex;flex-wrap:wrap;flex:1;}
.secondary-category{margin-right:3px;margin-bottom:3px;}
.secondary-category a{text-decoration:none;padding:2px 5px;font-size:14px;color:#0960bd!important;display:inline-block;}
.secondary-category a:hover{background-color:transparent;color:#EE3131;}

/* 布局样式 */
.content-wrap{display:flex;margin-top:5px;}
.left-column{flex:1;background:#fff;padding:0;}
.right-column{width:300px;background:transparent;padding:0;border:none;}

/* 通知框样式 */
.notice-box{background:#fff;border-radius:0;padding:0 10px;margin-bottom:15px;border:none;}
.notice-list{list-style:none;padding:8px 0;margin:0;background:#fff;}
.notice-list li{position:relative;padding:0 10px;margin-bottom:0;line-height:32px;font-size:14px;color:#333;border-bottom:1px solid #f5f5f5;}
.notice-list li:last-child{border-bottom:none;}
.notice-list li a{display:block;padding:0;transition:all 0.2s;color:#333;}
.notice-list li a:hover{color:#EE3131;text-decoration:none;}

/* 发布信息页面样式 */

.content-wrap{display:flex;gap:20px;}
.left-column{flex:1;background:#fff;border-radius:0;min-height:600px;}
.right-column{width:300px;}

/* 表单标题和当前栏目 */
.post-title{padding:15px 20px;border-bottom:1px solid #eee;display:flex;justify-content:space-between;align-items:center;}
.post-title h3{font-size:18px;color:#333;margin:0;}
.current-category{display:flex;align-items:center;height:36px;gap:10px;margin-top:5px;line-height:36px;}
.category-text{line-height:36px;color:#333;font-size:14px;}
.reselect-link{color:#0066cc;text-decoration:none;font-size:14px;}
.reselect-link:hover{color:#f60;text-decoration:underline;}

/* 表单面板 */
.form-panel{background:#fff;padding:20px;margin-bottom:20px;}
.form-group{display:flex;margin-bottom:5px;}
.form-group:last-child{margin-bottom:0;}
.form-group.required .form-label:before{content:"*";color:#ff3b30;margin-right:4px;font-size:16px;}
.form-label{width:100px;text-align:right;line-height:36px;margin-right:10px;color:#333;font-size:14px;}
.form-right{display:flex;align-items:center;gap:10px;min-height:36px;}

/* 表单控件 */
.form-input,.form-select,.form-textarea{width:300px;height:34px;border:1px solid #ddd;border-radius:0;padding:0 10px;font-size:14px;color:#333;box-sizing:border-box;line-height:34px;}
.form-input:focus,.form-select:focus,.form-textarea:focus{border-color:#409eff;outline:none;box-shadow:0 0 5px rgba(64,158,255,0.2);}
.form-input.error,.form-select.error,.form-textarea.error{border-color:#f56c6c;}
.form-input.valid,.form-select.valid,.form-textarea.valid{border-color:#67c23a;}
.form-textarea{height:80px;width:600px;padding:8px 10px;line-height:1.5;resize:none;min-height:80px;}
.form-hint{font-size:12px;color:#999;margin-top:4px;}

/* 表单字段提示图标 */
.field-icon{display:none;position:absolute;top:8px;left:310px;font-size:16px;}
.field-error{display:none;position:absolute;left:310px;top:6px;color:#f56c6c;font-size:13px;line-height:20px;background-color:#fff;border-radius:0;padding:2px 8px;white-space:nowrap;z-index:1;}
.field-error i{margin-right:4px;color:#f56c6c;font-size:16px;}
.field-error.show{display:inline-block;}
.field-success{display:none;position:absolute;left:310px;top:8px;color:#67c23a;font-size:16px;}
.field-success.show{display:inline-block;}
.field-focus{display:none;position:absolute;left:310px;top:8px;color:#409eff;font-size:16px;}
.field-focus.show{display:inline-block;}
.field-focus-tooltip{display:none;position:absolute;left:340px;top:5px;background:#e6f1fc;border:1px solid #b3d8ff;color:#409eff;font-size:13px;line-height:18px;padding:4px 10px;border-radius:4px;box-shadow:0 2px 4px rgba(0,0,0,0.1);white-space:nowrap;z-index:2;}
.field-focus.show + .field-focus-tooltip{display:block;}
.field-focus-tooltip:before{content:"";position:absolute;left:-6px;top:8px;width:10px;height:10px;background:#e6f1fc;border-left:1px solid #b3d8ff;border-bottom:1px solid #b3d8ff;transform:rotate(45deg);}

#region_id + .field-error, #region_id + .field-success, #region_id + .field-focus{left:210px;}
#expire_days + .field-error, #expire_days + .field-success, #expire_days + .field-focus{left:130px;}
#title + .field-error, #title + .field-success, #title + .field-focus{left:510px;}
#content + .field-error, #content + .field-success, #content + .field-focus{left:610px;}
#content + .field-error{top:160px;}
#content + .field-success, #content + .field-focus{top:160px;}
#contact_name + .field-error, #contact_name + .field-success, #contact_name + .field-focus, 
#contact_mobile + .field-error, #contact_mobile + .field-success, #contact_mobile + .field-focus, 
#contact_address + .field-error, #contact_address + .field-success, #contact_address + .field-focus, 
#contact_weixin + .field-error, #contact_weixin + .field-success, #contact_weixin + .field-focus, 
#password + .field-error, #password + .field-success, #password + .field-focus{left:210px;}

/* 图片上传 */
.image-upload{width:600px;padding:0;margin-bottom:10px;}
.hidden-input{display:none;}
.upload-area{display:flex;align-items:flex-start;gap:15px;padding:10px;margin-bottom:10px;}
.upload-btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    width: 90px;
    height: 90px;
    border: 1px solid #ddd;
    background: #fff;
    color: #666;
    cursor: pointer;
    font-size: 14px;
    flex-direction: column;
}

.upload-btn i {
    font-size: 24px;
    margin-bottom: 5px;
}

.upload-btn:hover {
    border-color: #409eff;
    color: #409eff;
}

.image-hint {
    flex: 1;
    color: #999;
    font-size: 12px;
    line-height: 1.5;
}

.image-hint .warning {
    color: #f56c6c;
}

.image-previews{display:flex;flex-wrap:wrap;gap:10px;}
.image-item{position:relative;width:90px;height:90px;cursor:move;border:2px solid transparent;}
.image-item.dragging{opacity:0.5;border:2px dashed #0066cc;}
.image-item.drag-over{border:2px dashed #0066cc;}
.image-item img{width:100%;height:100%;object-fit:cover;}
.remove-image{position:absolute;top:-8px;right:-8px;width:20px;height:20px;background:rgba(0,0,0,0.5);color:#fff;border-radius:50%;display:flex;align-items:center;justify-content:center;cursor:pointer;}

/* 微信号输入组 */
.weixin-group{display:flex;align-items:center;gap:10px;}
.weixin-checkbox{display:flex;align-items:center;white-space:nowrap;}
/* 发布须知完整样式 - 紧凑版 */
.posting-notice {
    margin: 0 10px 15px;
    background: #fff8db;
    border: 1px solid #ffd77a;
    border-radius: 4px;
    box-sizing: border-box;
}

.notice-title {
    margin: 0;
    padding: 8px 12px;
    background: #fff5cc;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #ffd77a;
}

.notice-title i.fa-exclamation-circle {
    color: #ff9900;
    font-size: 14px;
    margin-right: 6px;
}

.notice-title span {
    color: #333;
    font-size: 14px;
    font-weight: 600;
}

/* 移除折叠图标 */
.notice-title .toggle-icon {
    display: none;
}

.notice-content {
    padding: 8px 12px;
    font-size: 12px;
    line-height: 1.4;
    color: #666;
}

.notice-content p {
    margin: 0 0 4px;
    padding-left: 16px;
    position: relative;
}

.notice-content p:last-child {
    margin-bottom: 0;
}

.notice-content p:before {
    content: "①";
    position: absolute;
    left: 0;
    color: #ff9900;
}

.notice-content p:nth-child(2):before { content: "②"; }
.notice-content p:nth-child(3):before { content: "③"; }
.notice-content p:nth-child(4):before { content: "④"; }
.notice-content p:nth-child(5):before { content: "⑤"; }
.notice-content p:nth-child(6):before { content: "⑥"; }
.notice-content p:nth-child(7):before { content: "⑦"; }
.notice-content p:nth-child(8):before { content: "⑧"; }

.notice-content p.highlight {
    color: #f56c6c;
    font-weight: 500;
}

.notice-content .agreement-link {
    color: #0066cc;
    text-decoration: none;
}

.notice-content .agreement-link:hover {
    color: #f60;
    text-decoration: underline;
}

/* 提交按钮 */
.submit-group{padding:15px 0;text-align:center;margin-bottom:20px;}
.submit-button{width:200px;height:40px;background:#0066cc;color:#fff;border:none;font-size:16px;cursor:pointer;}
.submit-button:hover{background:#0052a3;}

/* 右侧面板 */
.side-panel{background:#fff;margin-bottom:20px;}
.panel-title{padding:15px;font-size:16px;color:#333;border-bottom:1px solid #eee;}
.panel-content{padding:15px;}

/* 发布流程 */
.process-list{}
.process-item{display:flex;align-items:center;margin-bottom:15px;}
.process-item:last-child{margin-bottom:0;}
.process-num{width:24px;height:24px;background:#0066cc;color:#fff;display:flex;align-items:center;justify-content:center;margin-right:10px;font-size:14px;}
.process-text{color:#666;}

/* 温馨提示 */
.tips-list{margin:0;padding:0;list-style:none;}
.tips-list li{position:relative;padding-left:12px;margin-bottom:10px;color:#666;}
.tips-list li:last-child{margin-bottom:0;}
.tips-list li:before{content:"";position:absolute;left:0;top:8px;width:4px;height:4px;background:#0066cc;border-radius:50%;}

/* 遮罩层 */
.loading-overlay, .success-overlay { position: fixed; top: 0; left: 0; width: 100%; height: 100%; background-color: rgba(0, 0, 0, 0.7); display: none; z-index: 9999; justify-content: center; align-items: center; } /* 弹窗遮罩层样式 */
.loading-overlay.show, .success-overlay.show { display: flex; } /* 显示弹窗遮罩层 */

/* 加载中弹窗 */
.loading-spinner { background-color: rgba(255, 255, 255, 0.95); border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); padding: 30px; text-align: center; position: relative; margin: auto; width: 200px; } /* 加载中弹窗容器 */
.loading-spinner i { color: #4CAF50; font-size: 36px; margin-bottom: 15px; animation: spin 1s infinite linear; } /* 加载中图标 */
.loading-text { font-size: 16px; color: #333; margin-top: 10px; } /* 加载中文本 */

/* 成功弹窗 */
.success-message { background: linear-gradient(to bottom, #ffffff, #f8f8f8); border-radius: 8px; box-shadow: 0 5px 15px rgba(0, 0, 0, 0.2); padding: 30px; width: 320px; text-align: center; position: relative; margin: auto; } /* 成功弹窗容器，固定宽度320px */
.success-message i { color: #4CAF50; font-size: 50px; margin-bottom: 15px; } /* 成功图标 */
.success-text { font-size: 22px; font-weight: bold; margin-bottom: 10px; color: #333; } /* 成功标题文本 */
.success-subtext { color: #666; margin-bottom: 20px; font-size: 16px; } /* 成功副标题文本 */
.success-btn { background: linear-gradient(to bottom, #4CAF50, #45a049); color: white; border: none; padding: 10px 30px; border-radius: 4px; font-size: 16px; cursor: pointer; transition: all 0.3s; min-width: 120px; } /* 成功按钮，最小宽度120px */
.success-btn:hover { background: linear-gradient(to bottom, #45a049, #3d8b3d); transform: translateY(-2px); box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2); } /* 成功按钮悬停效果 */

@keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } } /* 加载图标旋转动画 */

/* 错误提示 */
.error-message{background:#fff2f2;border:1px solid #ffccc7;color:#ff3b30;padding:10px 15px;margin-bottom:20px;display:flex;align-items:center;}
.error-message i{margin-right:8px;}

/* 表单控件宽度调整 */
.form-group.full-width .form-input,
.form-group.full-width .form-select,
.form-group.full-width .form-textarea{width:100%;}

/* 特定字段宽度 */
#region_id{width:120px;} /* 地区选择 */
#expire_days{width:120px;} /* 有效期选择 */
#title{width:500px;} /* 标题较长 */
#content{width:500px;height:160px;} /* 内容最宽且高度固定 */
#contact_name{width:130px;} /* 联系人名字较短 */
#mobile{width:130px;} /* 手机号固定11位 */
#contact_address{width:400px;} /* 地址较长 */
#contact_weixin{width:130px;} /* 微信号较短 */
#password{width: 130px;} /* 密码较短 */

.inputTip {
    display: none;  /* 默认隐藏 */
    color: #666;
    font-size: 13px;
}

.inputTip i {
    margin-right: 5px;
    font-size: 18px;  /* 再增大一点图标尺寸 */
    vertical-align: middle;
}

/* 焦点状态显示蓝色提示 */
.form-input:focus ~ .inputTip,
.form-select:focus ~ .inputTip,
.form-textarea:focus ~ .inputTip {
    display: inline-block;
    color: #409eff;
}

/* 错误状态 */
.form-input.error ~ .inputTip,
.form-select.error ~ .inputTip,
.form-textarea.error ~ .inputTip {
    display: inline-block;
    color: #f56c6c;
}

.form-input.error ~ .inputTip i,
.form-select.error ~ .inputTip i,
.form-textarea.error ~ .inputTip i {
    color: #f56c6c;
}

/* 成功状态 - 只显示图标 */
.form-input.valid ~ .inputTip,
.form-select.valid ~ .inputTip,
.form-textarea.valid ~ .inputTip {
    display: inline-block;
}

.form-input.valid ~ .inputTip i,
.form-select.valid ~ .inputTip i,
.form-textarea.valid ~ .inputTip i {
    color: #67c23a;
    margin-right: 0;  /* 移除图标右边距 */
}

/* 成功状态隐藏文字 */
.form-input.valid ~ .inputTip span,
.form-select.valid ~ .inputTip span,
.form-textarea.valid ~ .inputTip span {
    display: none;
}

/* 文本域的特殊处理 */
.form-group textarea + .inputTip {
    align-self: flex-start;
    margin-top: 10px;
}

.change-category {
    line-height: 36px;  /* 与表单label的line-height一致 */
    color: #0066cc;
    text-decoration: none;
    font-size: 14px;
    margin-left: 10px;
}

.change-category:hover {
    color: #f60;
    text-decoration: underline;
}

/* 当前栏目行样式 */
.category-row {
    display: flex;
    margin-bottom: 5px;
}

.category-content {
    color: #333;
    line-height: 36px;
    font-size: 14px;
    display: flex;
    align-items: center;
}

.change-link {
    color: #0066cc;
    text-decoration: none;
    margin-left: 10px;
    font-size: 14px;
}

.change-link:hover {
    color: #f60;
    text-decoration: underline;
}

/* 自定义下拉框 */
.custom-select {
    position: relative;
    width: 120px;
}

.select-trigger {
    width: 100%;
    height: 34px;
    line-height: 34px;
    border: 1px solid #ddd;
    padding: 0 10px;
    font-size: 14px;
    color: #333;
    background: #fff;
    cursor: pointer;
    position: relative;
    box-sizing: border-box;
}

.select-trigger:after {
    content: '';
    position: absolute;
    right: 10px;
    top: 50%;
    margin-top: -2px;
    border-width: 5px;
    border-style: solid;
    border-color: #999 transparent transparent transparent;
}

.select-dropdown {
    display: none;
    position: absolute;
    top: 100%;
    left: 0;
    width: 100%;
    background: #fff;
    border: 1px solid #ddd;
    border-top: none;
    max-height: 300px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 2px 6px rgba(0,0,0,0.1);
}

.select-group {
    border-bottom: 1px solid #eee;
}

.select-group:last-child {
    border-bottom: none;
}

.group-label {
    padding: 8px 10px;
    font-size: 14px;
    color: #999;
    background: #f8f8f8;
}

.group-options {
    padding: 5px 0;
}

.select-option {
    padding: 8px 10px;
    font-size: 14px;
    color: #333;
    cursor: pointer;
    line-height: 1.4;
}

.select-option:hover {
    background: #f5f7fa;
}

.select-option.selected {
    color: #409eff;
    font-weight: bold;
}

.custom-select.active .select-dropdown {
    display: block;
}

.custom-select.active .select-trigger {
    border-color: #409eff;
}

/* 自定义下拉框的错误和成功状态 */
.custom-select.error .select-trigger {
    border-color: #f56c6c;
}

.custom-select.valid .select-trigger {
    border-color: #67c23a;
}

/* 自定义下拉框的提示样式 */
.custom-select ~ .inputTip {
    margin-left: 10px;
    display: none;
}

.custom-select.active ~ .inputTip {
    display: inline-block;
    color: #409eff;
}

.custom-select.error ~ .inputTip {
    display: inline-block;
    color: #f56c6c;
}

.custom-select.valid ~ .inputTip {
    display: inline-block;
}

.custom-select.valid ~ .inputTip i {
    color: #67c23a;
}

.custom-select.valid ~ .inputTip i.fa-info-circle {
    display: none;
}

.custom-select.valid ~ .inputTip i.fa-check-circle {
    display: inline-block;
}

.custom-select.valid ~ .inputTip span {
    display: none;
}

/* 调整提示图标位置 */
#region_id ~ .inputTip,
#expire_days ~ .inputTip {
    position: relative;
    left: auto;
    top: auto;
}