RewriteEngine On

RewriteCond %{REQUEST_FILENAME} -f [OR]
RewriteCond %{REQUEST_FILENAME} -d
RewriteRule ^ - [L]

RewriteRule ^([a-zA-Z0-9_-]+)/$ category.php?pinyin=$1 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/([0-9]+)\.html$ view.php?id=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/p([0-9]+)/$ category.php?pinyin=$1&page=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/a([0-9]+)/$ category.php?pinyin=$1&area=$2 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/a([0-9]+)p([0-9]+)/$ category.php?pinyin=$1&area=$2&page=$3 [L,QSA]

RewriteRule ^([a-zA-Z0-9_-]+)/page/([0-9]+)/$ category.php?pinyin=$1&page=$2 [L,QSA]

RewriteCond %{REQUEST_URI} ^/admin/
RewriteRule ^ - [L]