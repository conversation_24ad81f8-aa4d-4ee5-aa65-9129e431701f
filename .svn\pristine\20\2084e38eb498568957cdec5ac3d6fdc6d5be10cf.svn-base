/**
 * 公共功能脚本
 */

// 添加返回顶部按钮到页面
function addFloatButtons() {
    // 检查是否已存在按钮组
    if (document.querySelector('.float-btns')) {
        return;
    }
    
    // 创建按钮组
    var floatBtns = document.createElement('div');
    floatBtns.className = 'float-btns';
    
    // 返回顶部按钮
    var backTopBtn = document.createElement('a');
    backTopBtn.href = 'javascript:;';
    backTopBtn.id = 'backTop';
    backTopBtn.className = 'float-btn top';
    backTopBtn.innerHTML = '<i class="fas fa-arrow-up"></i>';
    
    // 添加到按钮组
    floatBtns.appendChild(backTopBtn);
    
    // 添加到页面
    document.body.appendChild(floatBtns);
    
    // 添加返回顶部功能
    window.addEventListener('scroll', function() {
        if (window.pageYOffset > 300) {
            backTopBtn.classList.add('visible');
        } else {
            backTopBtn.classList.remove('visible');
        }
    });
    
    backTopBtn.addEventListener('click', function() {
        window.scrollTo({
            top: 0,
            behavior: 'smooth'
        });
    });
}

// 添加悬浮按钮样式到页面
function addFloatButtonsStyle() {
    // 检查是否已存在样式
    if (document.getElementById('float-btns-style')) {
        return;
    }
    
    // 创建样式元素
    var style = document.createElement('style');
    style.id = 'float-btns-style';
    style.textContent = `
        .float-btns {
            position: fixed;
            right: 15px;
            bottom: 80px;
            z-index: 99;
            display: flex;
            flex-direction: column;
            opacity: 0.6;
            transition: opacity 0.3s, transform 0.3s;
        }
        .float-btns:hover, .float-btns:active {
            opacity: 1;
        }
        .float-btn {
            width: 45px;
            height: 45px;
            border-radius: 50%;
            background-color: rgba(255, 255, 255, 0.9);
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 10px;
            color: #666;
            text-decoration: none;
            font-size: 18px;
            transition: all 0.3s ease;
            border: 1px solid #eee;
        }
        .float-btn:active {
            transform: scale(0.9);
        }
        .float-btn.top {
            background-color: rgba(0, 0, 0, 0.6);
            color: #fff;
            font-size: 17px;
            border: none;
            box-shadow: 0 2px 10px rgba(0, 0, 0, 0.2);
        }
        #backTop {
            opacity: 0;
            transform: translateY(20px);
            transition: opacity 0.3s, transform 0.3s;
        }
        #backTop.visible {
            opacity: 1;
            transform: translateY(0);
        }
    `;
    
    // 添加到页面
    document.head.appendChild(style);
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    addFloatButtonsStyle();
    addFloatButtons();
    
    // 添加滚动事件处理，在用户阅读内容时自动隐藏悬浮按钮
    let isScrolling = false;
    let scrollTimer = null;
    let buttonsVisible = true;
    const floatBtns = document.querySelector('.float-btns');
    
    if (floatBtns) {
        window.addEventListener('scroll', function() {
            if (!isScrolling) {
                isScrolling = true;
                
                // 快速滚动时暂时隐藏按钮
                if (buttonsVisible) {
                    floatBtns.style.transform = 'translateX(70px)';
                    buttonsVisible = false;
                }
            }
            
            // 清除之前的定时器
            clearTimeout(scrollTimer);
            
            // 滚动停止后延迟显示按钮
            scrollTimer = setTimeout(function() {
                isScrolling = false;
                floatBtns.style.transform = 'translateX(0)';
                buttonsVisible = true;
                
                // 检查是否在页面底部附近，如果是则调整位置避免与底部导航重叠
                const scrollHeight = document.documentElement.scrollHeight;
                const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
                const clientHeight = document.documentElement.clientHeight;
                
                if (scrollTop + clientHeight > scrollHeight - 100) {
                    floatBtns.style.bottom = '100px'; // 靠近底部时上移
                } else {
                    floatBtns.style.bottom = '80px'; // 正常位置
                }
            }, 150);
        });
    }
});