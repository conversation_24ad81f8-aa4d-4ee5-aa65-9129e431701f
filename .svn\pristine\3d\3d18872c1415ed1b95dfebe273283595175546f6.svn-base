# 缓存预热系统使用指南

## 概述

缓存预热系统通过定时任务自动生成和更新网站缓存，确保用户访问时能够快速获取内容，显著提升网站性能和用户体验。

## 系统架构

```
缓存预热系统
├── scripts/cache_warmup.php          # 核心预热脚本
├── scripts/setup_cron.sh             # 定时任务安装脚本
├── admin/cache_warmup_manager.php    # 后台管理界面
└── docs/cache_warmup_guide.md        # 使用指南
```

## 功能特性

### ✅ 核心功能
- **自动预热**: 定时执行缓存预热任务
- **智能策略**: 优先预热热门内容和重要页面
- **多层缓存**: 支持首页、分类页、详情页、地区页缓存
- **过期清理**: 自动清理过期缓存文件
- **性能监控**: 详细的执行统计和日志记录

### ✅ 预热内容
1. **基础数据缓存**
   - 分类数据缓存
   - 地区数据缓存

2. **页面缓存**
   - 首页缓存
   - 分类列表页缓存
   - 地区列表页缓存
   - 热门信息详情页缓存

3. **智能优化**
   - 基于访问量的热门内容优先预热
   - 避免过度占用服务器资源
   - 支持批量处理和进度控制

## 安装配置

### 1. 快速安装

```bash
# 进入项目目录
cd /path/to/your/project

# 给安装脚本执行权限
chmod +x scripts/setup_cron.sh

# 运行安装脚本
./scripts/setup_cron.sh
```

### 2. 手动安装

#### 2.1 设置定时任务

编辑crontab：
```bash
crontab -e
```

添加以下任意一行：

**方案1: 每6小时执行一次（推荐高流量网站）**
```bash
0 */6 * * * /usr/bin/php /path/to/project/scripts/cache_warmup.php >> /path/to/project/data/logs/cron_cache_warmup.log 2>&1
```

**方案2: 每天凌晨2点执行（推荐中低流量网站）**
```bash
0 2 * * * /usr/bin/php /path/to/project/scripts/cache_warmup.php >> /path/to/project/data/logs/cron_cache_warmup.log 2>&1
```

#### 2.2 配置安全密钥

编辑 `scripts/cache_warmup.php`，修改安全密钥：
```php
$secret_key = 'your_secret_key_here'; // 修改为您的密钥
```

#### 2.3 创建日志目录

```bash
mkdir -p /path/to/project/data/logs
chmod 755 /path/to/project/data/logs
```

## 使用方法

### 1. 后台管理

访问后台管理界面：
```
http://yourdomain.com/admin/cache_warmup_manager.php
```

功能包括：
- 查看缓存统计信息
- 手动执行缓存预热
- 配置预热参数
- 查看执行日志

### 2. 命令行执行

```bash
# 手动执行缓存预热
php /path/to/project/scripts/cache_warmup.php

# 查看帮助信息
php /path/to/project/scripts/cache_warmup.php --help
```

### 3. Web接口执行

```bash
# 通过Web接口执行（需要密钥）
curl "http://yourdomain.com/scripts/cache_warmup.php?key=your_secret_key"
```

## 配置参数

### 缓存时间配置

在 `config/config.inc.php` 中调整：

```php
$config['cache_enable'] = 1;           // 启用缓存
$config['cache_index'] = 3600;         // 首页缓存时间（秒）
$config['cache_list'] = 1800;          // 列表页缓存时间（秒）
$config['cache_post'] = 1800;          // 详情页缓存时间（秒）
$config['cache_category'] = 7200;      // 分类页缓存时间（秒）
$config['cache_region'] = 7200;        // 地区页缓存时间（秒）
$config['cache_search'] = 600;         // 搜索缓存时间（秒）
$config['cache_data'] = 3600;          // 数据缓存时间（秒）
```

### 预热策略配置

通过后台管理界面或数据库设置：

| 参数 | 说明 | 默认值 |
|------|------|--------|
| warmup_enable | 是否启用自动预热 | 1 |
| warmup_interval | 预热间隔（小时） | 6 |
| warmup_popular_posts_limit | 热门信息预热数量 | 50 |
| warmup_category_limit | 分类预热限制 | 0（不限制） |
| warmup_region_limit | 地区预热限制 | 0（不限制） |
| warmup_clean_expired | 是否清理过期缓存 | 1 |

## 监控和维护

### 1. 查看执行日志

```bash
# 查看今天的预热日志
tail -f /path/to/project/data/logs/cache_warmup_$(date +%Y-%m-%d).log

# 查看cron执行日志
tail -f /path/to/project/data/logs/cron_cache_warmup.log
```

### 2. 监控指标

关键监控指标：
- **执行时间**: 正常应在5分钟内完成
- **成功率**: 应保持在95%以上
- **缓存命中率**: 预热后应显著提升
- **服务器负载**: 执行期间负载不应过高

### 3. 性能优化

#### 3.1 调整执行频率

根据网站访问量调整：
- **高流量网站**: 每2-4小时执行一次
- **中流量网站**: 每6-8小时执行一次
- **低流量网站**: 每天执行一次

#### 3.2 优化预热策略

```php
// 根据服务器性能调整批处理大小
// 在 cache_warmup.php 中修改：

// 分类处理间隔
if ($count % 10 == 0) {
    usleep(100000); // 休息0.1秒，可根据服务器性能调整
}

// 信息处理间隔
if ($count % 5 == 0) {
    usleep(50000); // 休息0.05秒
}
```

## 故障排除

### 常见问题

#### 1. 定时任务不执行

**检查步骤：**
```bash
# 检查crontab是否正确设置
crontab -l

# 检查PHP路径是否正确
which php

# 检查脚本权限
ls -la /path/to/project/scripts/cache_warmup.php

# 手动测试执行
php /path/to/project/scripts/cache_warmup.php
```

#### 2. 执行超时

**解决方案：**
- 增加脚本执行时间限制
- 减少预热内容数量
- 优化数据库查询性能
- 增加处理间隔时间

#### 3. 内存不足

**解决方案：**
```php
// 在脚本开头增加内存限制
ini_set('memory_limit', '512M');

// 或者减少批处理大小
$pageSize = 10; // 减少每次处理的数量
```

#### 4. 数据库连接失败

**检查步骤：**
- 验证数据库配置文件
- 检查数据库服务状态
- 确认数据库用户权限

### 日志分析

#### 正常执行日志示例：
```
[2024-01-15 02:00:01] 缓存预热任务开始执行
[2024-01-15 02:00:02] 预热基础数据缓存...
[2024-01-15 02:00:03] 分类数据缓存已预热，共 25 个分类
[2024-01-15 02:00:04] 地区数据缓存已预热，共 15 个地区
[2024-01-15 02:00:05] 预热首页缓存...
[2024-01-15 02:00:06] 首页缓存预热成功
[2024-01-15 02:00:07] 预热分类页面缓存...
[2024-01-15 02:00:15] 分类页面缓存预热完成，共处理 25 个分类
[2024-01-15 02:00:16] 预热热门信息详情页缓存...
[2024-01-15 02:00:25] 热门信息详情页缓存预热完成，共处理 50 条信息
[2024-01-15 02:00:26] 清理过期缓存完成，共清理 12 个文件
[2024-01-15 02:00:27] 缓存预热完成
[2024-01-15 02:00:27] === 缓存预热统计 ===
[2024-01-15 02:00:27] 执行时间: 26.34 秒
[2024-01-15 02:00:27] 总页面数: 90
[2024-01-15 02:00:27] 成功页面: 90
[2024-01-15 02:00:27] 失败页面: 0
[2024-01-15 02:00:27] 成功率: 100%
```

## 最佳实践

### 1. 部署建议

- **测试环境**: 先在测试环境验证配置
- **逐步部署**: 从低频率开始，逐步增加预热频率
- **监控观察**: 部署后密切监控服务器性能

### 2. 性能优化

- **错峰执行**: 选择网站访问量较低的时间段执行
- **分批处理**: 避免一次性处理过多内容
- **资源控制**: 合理设置内存和时间限制

### 3. 安全考虑

- **访问控制**: 设置强密钥保护Web接口
- **日志管理**: 定期清理和归档日志文件
- **权限控制**: 确保脚本文件权限设置正确

## 效果评估

### 性能指标

部署缓存预热后，通常可以观察到：

- **页面加载速度**: 提升30-70%
- **服务器响应时间**: 减少50-80%
- **数据库查询次数**: 减少60-90%
- **用户体验**: 显著改善

### 监控工具

推荐使用以下工具监控效果：
- **Google PageSpeed Insights**: 页面性能分析
- **GTmetrix**: 网站速度测试
- **服务器监控**: CPU、内存、磁盘使用率
- **数据库监控**: 查询性能和连接数

## 技术支持

如遇到问题，请：

1. 查看执行日志确定具体错误
2. 检查服务器资源使用情况
3. 验证配置参数是否正确
4. 测试手动执行是否正常

---

**注意**: 缓存预热会增加服务器负载，请根据服务器性能合理配置执行频率和预热内容数量。
