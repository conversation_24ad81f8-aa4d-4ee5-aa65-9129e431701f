/* 面包屑导航样式 - 扁平化设计 */
.breadcrumb-container {
    margin: 0px 0 10px 0;
    padding: 10px;
    border: none;
    background-color: #fff;
 
    width: 100%;
    box-sizing: border-box;
    margin-top: 10px;
}

.breadcrumb {
    font-size: 14px;
    color: #666;
    display: flex;
    align-items: center;
    flex-wrap: wrap;
}

.breadcrumb a {
    color: #666;
    text-decoration: none;
    transition: color 0.3s;
}

.breadcrumb a:hover {
    color: #EE3131;
    text-decoration: none;
}

.breadcrumb .separator {
    margin: 0 8px;
    color: #ccc;
}


/* 新闻页面布局 */
.news-container {
    display: flex;
    justify-content: space-between;

}



.news-right {
    width: 300px;
}

/* 新闻列表样式 - 图文混排（紧凑版） */
.news-item {
    padding: 15px 0;  /* 原来是25px，减少上下内边距 */
    border-bottom: 1px solid #eee;
}

.news-item:first-child {
    padding-top: 0;
}

.news-item:last-child {
    border-bottom: none;
    padding-bottom: 0;
}

/* 图文新闻样式 */
.news-item-img {
    display: flex;
    gap: 15px;  /* 原来是20px，减少图片和内容的间距 */
}

.news-img {
    flex: 0 0 180px;  /* 原来是220px，减少图片宽度 */
    height: 120px;    /* 原来是150px，减少图片高度 */
    overflow: hidden;
    border-radius: 4px;  /* 减小圆角 */
}

.news-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: transform 0.5s;
}

.news-img:hover img {
    transform: scale(1.05);
}

.news-content {
    flex: 1;
    display: flex;
    flex-direction: column;
}

/* 纯文本新闻样式 */
.news-item-text {
    padding-left: 0;
    padding-top: 12px;
    padding-bottom: 12px;
}

/* 通用新闻元素样式 */
.news-item h3 {
    font-size: 18px;    /* 原来是20px，减小标题字体 */
    margin-bottom: 8px;  /* 原来是12px，减少标题下方间距 */
    font-weight: 600;
    line-height: 1.3;    /* 原来是1.4，减少行高 */
}

.news-item h3 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s;
}

.news-item h3 a:hover {
    color: #EE3131;
}

.news-item .meta {
    color: #999;
    font-size: 12px;      /* 原来是13px，减小元数据字体 */
    margin-bottom: 8px;   /* 原来是12px，减少元数据下方间距 */
}

.news-item .meta span {
    margin-right: 15px;   /* 原来是20px，减少元数据项间距 */
    display: inline-block;
}

.news-item .desc {
    color: #666;
    font-size: 13px;      /* 原来是14px，减小描述文本字体 */
    line-height: 1.6;     /* 原来是1.8，减少行高 */
    text-align: justify;
}

/* 纯文本新闻的描述文字 */
.news-item-text .desc {
    line-height: 1.6;
    max-height: 3.2em;    /* 控制为2行 */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 2; /* 原来是3，减少为2行 */
    -webkit-box-orient: vertical;
}

/* 图文新闻的描述文字 */
.news-item-img .desc {
    max-height: 1.6em;    /* 控制为1行 */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-line-clamp: 1; /* 原来是2，减少为1行 */
    -webkit-box-orient: vertical;
}

/* 分页样式调整 */
.pagination {
    margin-top: 20px;    /* 原来是30px，减少上方间距 */
    text-align: center;
    font-size: 0;
}

.pagination a {
    display: inline-block;
    padding: 6px 12px;    /* 原来是8px 15px，减少内边距 */
    margin: 0 3px;        /* 原来是0 4px，减少间距 */
    border: 1px solid #e5e5e5;
    color: #666;
    text-decoration: none;
    font-size: 13px;      /* 原来是14px，减小字体 */
    border-radius: 3px;
    transition: all 0.3s;
}
/* 右侧热门信息样式 */
.hot-news {
    background: #fff;
    padding: 10px;
    margin-bottom: 10px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
    border-radius: 4px;
}

.hot-news h2 {
    font-size: 18px;
    color: #333;
    padding-bottom: 15px;
    border-bottom: 2px solid #EE3131;
    margin-bottom: 10px;
    font-weight: 600;
}

.hot-news-item {
    display: flex;
    margin-bottom: 15px;
    padding-bottom: 15px;
    border-bottom: 1px solid #eee;
}

.hot-news-item:last-child {
    margin-bottom: 0;
    padding-bottom: 0;
    border-bottom: none;
}

.hot-news-item img {
    width: 120px;
    height: 80px;
    object-fit: cover;
    border-radius: 3px;
    margin-right: 12px;
}

.hot-news-item .info {
    flex: 1;
    overflow: hidden;
}

.hot-news-item .info h4 {
    font-size: 15px;
    margin-bottom: 8px;
    line-height: 1.4;
}

.hot-news-item .info h4 a {
    color: #333;
    text-decoration: none;
    transition: color 0.3s;
}

.hot-news-item .info h4 a:hover {
    color: #EE3131;
}

.hot-news-item .info p {
    color: #999;
    font-size: 12px;
}

/* 热门新闻列表样式 */
.hot-news-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.hot-news-list li {
    margin-bottom: 15px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    line-height: 1.4;
}

.hot-news-list li:last-child {
    margin-bottom: 0;
}

.hot-news-list li a {
    color: #333;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
    display: block;
    overflow: hidden;
    text-overflow: ellipsis;
}

.hot-news-list li a:hover {
    color: #EE3131;
}

.hot-news-list .num {
    display: inline-block;
    width: 20px;
    height: 20px;
    line-height: 20px;
    text-align: center;
    background: #ddd;
    color: #fff;
    font-size: 12px;
    margin-right: 10px;
    border-radius: 3px;
    float: left;
}

.hot-news-list li:nth-child(1) .num,
.hot-news-list li:nth-child(2) .num,
.hot-news-list li:nth-child(3) .num {
    background: #EE3131;
}



/* 右侧模块样式 */
.bbs-hot {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 0 10px 10px 10px;

}

.bbs-hot img {
    width: 100%;
    height: auto;
    margin: 5px 0;
    box-sizing: border-box;
    display: block;
}

/* 响应式适配 */
@media screen and (max-width: 1200px) {
    .news-left {
        width: 70%;
    }
    
    .news-right {
        width: 28%;
    }
    
    .hot-news-item img {
        width: 100px;
        height: 70px;
    }
}

@media screen and (max-width: 768px) {
    .news-container {
        flex-direction: column;
    }
    
    .news-left,
    .news-right {
        width: 100%;
        margin-bottom: 20px;
    }
    
    .news-item h3 {
        font-size: 18px;
    }
}

/* 新闻详情页样式 */
/* 新闻详情头部 */
.news-detail-header {
    margin-bottom: 20px;
    border-bottom: 1px solid #eee;
    padding-bottom: 15px;
}

.news-detail-title {
    font-size: 24px;
    color: #333;
    font-weight: 700;
    line-height: 1.4;
    margin-bottom: 15px;
}

.news-detail-meta {
    color: #999;
    font-size: 13px;
    display: flex;
    flex-wrap: wrap;
    align-items: center;
}

.news-detail-meta span {
    margin-right: 15px;
    display: inline-flex;
    align-items: center;
}

.news-detail-meta i {
    margin-right: 5px;
    display: inline-block;
    width: 16px;
    height: 16px;
    background-size: contain;
    background-repeat: no-repeat;
    vertical-align: middle;
}

.news-share {
    cursor: pointer;
    color: #666;
    transition: color 0.2s;
}

.news-share:hover {
    color: #EE3131;
}

/* 新闻摘要 */
.news-detail-summary {
    background-color: #f9f9f9;
    padding: 15px;
    border-left: 4px solid #EE3131;
    margin-bottom: 20px;
    color: #666;
    font-size: 14px;
    line-height: 1.8;
}

/* 新闻正文 */
.news-detail-content {
    line-height: 1.8; 
    color: #333; 
    font-size: 16px;
    white-space: pre-wrap; /* 保留空白符和换行符 */
}

.news-detail-content p {
    margin-bottom: 15px; 
}

.news-detail-content p:last-child {
    margin-bottom: 0;
}

.news-detail-img {
    margin: 20px 0;
    text-align: center;
}

.news-detail-img img {
    max-width: 100%;
    height: auto;
    border-radius: 4px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.img-caption {
    text-align: center;
    color: #999;
    font-size: 13px;
    margin-top: 10px;
}

/* 点赞和收藏 */
.news-detail-interaction {
    display: flex;
    justify-content: center;
    margin: 30px 0;
}

.interaction-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    margin: 0 25px;
    cursor: pointer;
}

.interaction-item i {
    display: block;
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background-color: #f5f5f5;
    text-align: center;
    line-height: 40px;
    font-size: 20px;
    color: #999;
    margin-bottom: 8px;
    transition: all 0.3s;
}

.interaction-item span {
    font-size: 14px;
    color: #666;
}

.interaction-item em {
    font-style: normal;
    font-size: 13px;
    color: #999;
    margin-top: 5px;
}

.interaction-item:hover i {
    background-color: #ffebeb;
    color: #EE3131;
}

/* 上一篇下一篇导航 */
.news-detail-nav {
    margin: 30px 0;
    border-top: 1px dashed #eee;
    border-bottom: 1px dashed #eee;
    padding: 15px 0;
}

.prev-next {
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.prev, .next {
    display: flex;
    align-items: flex-start;
}

.prev span, .next span {
    flex-shrink: 0;
    color: #666;
    font-size: 14px;
    font-weight: 600;
    margin-right: 5px;
}

.prev a, .next a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
    line-height: 1.6;
}

.prev a:hover, .next a:hover {
    color: #EE3131;
}

/* 相关推荐 */
.news-detail-related {
    margin-top: 30px;
}

.related-title {
    font-size: 18px;
    margin-bottom: 15px;
    color: #333;
    font-weight: 600;
    position: relative;
    border-bottom: 1px solid #eee;
    padding-bottom: 10px;
}

.related-title span {
    display: inline-block;
    padding-bottom: 10px;
    border-bottom: 2px solid #EE3131;
    position: relative;
    bottom: -1px;
}

.related-list {
    list-style: none;
    padding: 0;
    margin: 0;
}

.related-list li {
    position: relative;
    padding-left: 15px;
    margin-bottom: 12px;
    line-height: 1.6;
}

.related-list li:before {
    content: '';
    position: absolute;
    left: 0;
    top: 8px;
    width: 6px;
    height: 6px;
    background: #EE3131;
    border-radius: 50%;
}

.related-list li a {
    color: #666;
    text-decoration: none;
    font-size: 14px;
    transition: color 0.3s;
}

.related-list li a:hover {
    color: #EE3131;
}

/* 右侧广告 */
.side-ad {
    background: #fff;
    border-radius: 4px;
    overflow: hidden;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.side-ad img {
    display: block;
    width: 100%;
    height: auto;
}

/* 响应式样式补充 */
@media screen and (max-width: 768px) {
    .news-detail-title {
        font-size: 20px;
    }
    
    .news-detail-content {
        font-size: 15px;
    }
    
    .news-detail-meta span {
        margin-bottom: 5px;
    }
}

/* 新闻焦点区域样式 */
.news-focus {
    background: #fff;
    padding: 15px;
    margin-bottom: 20px;
    border-radius: 4px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.focus-slide {
    display: flex;
    margin-top: 15px;
    gap: 20px;
}

.focus-img {
    flex: 1.5;
    position: relative;
    height: 280px;
    overflow: hidden;
    border-radius: 4px;
}

.focus-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    transition: all 0.3s;
}

.focus-img img:hover {
    transform: scale(1.05);
}

.focus-img-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 10px 15px;
    background: rgba(0,0,0,0.6);
    color: #fff;
}

.focus-img-title h3 {
    margin: 0;
    font-size: 16px;
    font-weight: normal;
}

.focus-list {
    flex: 1;
}

.focus-list-item {
    padding: 10px 0;
    border-bottom: 1px dashed #eee;
}

.focus-list-item:last-child {
    border-bottom: none;
}

.focus-list-item h3 {
    font-size: 15px;
    margin: 0 0 5px 0;
    font-weight: normal;
}

.focus-list-item h3 a {
    color: #333;
    text-decoration: none;
}

.focus-list-item h3 a:hover {
    color: #EE3131;
}

.focus-list-item .meta {
    color: #999;
    font-size: 12px;
}

.focus-list-item .meta span {
    margin-right: 10px;
}

/* 栏目新闻样式 */
.cat-news {
    background: #fff;
    padding: 15px;
    border-radius: 4px;
    margin-bottom: 20px;
    box-shadow: 0 2px 6px rgba(0,0,0,0.05);
}

.cat-news-list {
    display: flex;
    flex-wrap: wrap;
    margin-top: 15px;
}

.cat-news-item {
    flex: 0 0 calc(50% - 10px);
    margin-bottom: 15px;
    padding-bottom: 10px;
    border-bottom: 1px dashed #eee;
}

.cat-news-item:nth-child(odd) {
    margin-right: 20px;
}

.cat-news-item h3 {
    font-size: 15px;
    margin: 0 0 5px 0;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    font-weight: normal;
}

.cat-news-item h3 a {
    color: #333;
    text-decoration: none;
}

.cat-news-item h3 a:hover {
    color: #EE3131;
}

.cat-news-item .meta {
    color: #999;
    font-size: 12px;
}

.cat-news-item .meta span {
    margin-right: 10px;
}

/* 响应式调整 */
@media screen and (max-width: 1200px) {
    .focus-slide {
        flex-direction: column;
    }
    
    .focus-img {
        height: 220px;
    }
    
    .cat-news-item {
        flex: 0 0 100%;
    }
}

@media screen and (max-width: 768px) {
    .news-container {
        flex-direction: column;
    }
    
    .news-left,
    .news-right {
        width: 100%;
    }
    
    .news-right {
        margin-top: 20px;
    }
}

/* 新闻头条风格样式 - 重新设计 */
.news-header {
    padding: 10px 0;
    border-bottom: 1px solid #f0f0f0;
    margin-bottom: 20px;
}



/* 新闻容器布局 */
.news-container {
    display: flex;
    justify-content: space-between;
}

.news-left {
    width: 908px;
    padding: 10px;
    box-shadow: none;
    border-radius: 0;
    background-color: #fff;
    margin-right: 10px;
    border-radius: 4px;
}

/* 通用标题样式 */
.news-title {
    font-size: 20px;
    margin-bottom: 20px;
    border-bottom: 2px solid #f0f0f0;
    position: relative;
}

.news-title h3 {
    display: inline-block;
    font-size: 20px;
    font-weight: bold;
    margin: 0;
    padding-bottom: 10px;
    color: #333;
    border-bottom: 2px solid #EE3131;
    margin-bottom: -2px;
}

.news-title .more {
    position: absolute;
    right: 0;
    bottom: 10px;
    font-size: 14px;
    color: #999;
    text-decoration: none;
}

.news-title .more:hover {
    color: #EE3131;
}

/* 新闻焦点区域 */
.news-focus {
    margin-bottom: 30px;
    background: none;
    padding: 0;
    box-shadow: none;
    border-radius: 0;
}

.focus-content {
    display: flex;
    gap: 30px;
}

.focus-main {
    flex: 2;
    position: relative;
    height: 360px;
    overflow: hidden;
}

.focus-main img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.focus-main-title {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 15px 20px;
    background: linear-gradient(to top, rgba(0,0,0,0.8), transparent);
}

.focus-main-title h3 {
    margin: 0;
    font-size: 20px;
    font-weight: bold;
    color: #fff;
}

.focus-list {
    flex: 1;
    display: flex;
    flex-direction: column;
}

.focus-item {
    padding: 15px 0;
    border-bottom: 1px solid #f0f0f0;
}

.focus-item:last-child {
    border-bottom: none;
}

.focus-item a {
    display: block;
    font-size: 16px;
    font-weight: bold;
    color: #333;
    text-decoration: none;
    margin-bottom: 8px;
    line-height: 1.4;
}

.focus-item a:hover {
    color: #EE3131;
}

.item-meta {
    color: #999;
    font-size: 13px;
}

.item-meta span {
    margin-right: 15px;
}

/* 新闻区块样式 */
.news-block {
    margin-bottom: 30px;
    padding-bottom: 20px;
}

.news-list {
    border-top: none;
}

/* 头条新闻 */
.headline-news {
    display: flex;
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #f0f0f0;
}

.headline-img {
    width: 240px;
    height: 160px;
    margin-right: 20px;
    overflow: hidden;
}

.headline-img img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.headline-info {
    flex: 1;
}

.headline-info h3 {
    margin: 0 0 10px 0;
    font-size: 18px;
    font-weight: bold;
}

.headline-info h3 a {
    color: #333;
    text-decoration: none;
}

.headline-info h3 a:hover {
    color: #EE3131;
}

.headline-desc {
    color: #666;
    font-size: 14px;
    line-height: 1.6;
    margin-top: 10px;
}

/* 列表新闻 */
.news-item {
    padding: 10px 0;
    border-bottom: 1px solid #f7f7f7;
}

.news-item:last-child {
    border-bottom: none;
}

.news-item h3 {
    margin: 0 0 5px 0;
    font-size: 16px;
    font-weight: normal;
}

.news-item h3 a {
    color: #333;
    text-decoration: none;
}

.news-item h3 a:hover {
    color: #EE3131;
}

/* 右侧模块通用样式 */
.bbs-hot {
    background-color: #fff;
    border-radius: 4px;
    margin-bottom: 10px;
    padding: 0 10px 10px 10px;
}

.yui-h-title {
    position: relative;
    font-size: 18px;
    margin-bottom: 15px;
    border-bottom: 1px solid #f0f0f0;
    padding-bottom: 10px;
}

/* 面包屑导航样式 - 扁平化设计 */



/* 响应式调整 */
@media screen and (max-width: 1200px) {
    .focus-content {
        flex-direction: column;
    }
    
    .focus-main {
        height: 320px;
    }
    
    .headline-img {
        width: 200px;
        height: 140px;
    }
}

@media screen and (max-width: 768px) {
    .news-container {
        flex-direction: column;
    }
    
    .news-left,
    .news-right {
        width: 100%;
    }
    
    .headline-news {
        flex-direction: column;
    }
    
    .headline-img {
        width: 100%;
        height: auto;
        margin-right: 0;
        margin-bottom: 15px;
    }
}

/* 置顶和推荐标签样式 */
.top-tag, .rec-tag {
    display: inline-block;
    padding: 0 5px;
    height: 18px;
    line-height: 18px;
    font-size: 12px;
    color: #fff;
    margin-left: 5px;
    border-radius: 2px;
    vertical-align: middle;
    font-weight: normal;
}

.top-tag {
    background-color: #f44336;
}

.rec-tag {
    background-color: #ff9800;
}

/* 分页样式 */
.pagebar {
    margin: 20px 0;
    text-align: center;
    font-size: 0;
}

.pagebar a, .pagebar span {
    display: inline-block;
    padding: 4px 8px; /* 更小的内边距 */
    font-size: 12px;  /* 更小的字体 */
    margin: 0 3px;
    border: 1px solid #e5e5e5;
    background: #fff;
    color: #333;
    text-decoration: none;
    vertical-align: middle;
    transition: all 0.3s;
}

.pagebar a:hover {
    background: #f5f5f5;
    color: #EE3131;
    border-color: #EE3131;
}

.pagebar span.current {
    background: #EE3131;
    color: #fff;
    border-color: #EE3131;
}

.pagebar .disabled {
    color: #999;
    cursor: not-allowed;
}

/* 旧版兼容 - 对应传统简单分页 */
.pagination {
    margin: 20px 0;
    text-align: center;
}

.pagination a, .pagination span {
    display: inline-block;
    padding: 4px 8px; /* 更小的内边距 */
    font-size: 12px;  /* 更小的字体 */
    margin: 0 3px;
    border: 1px solid #e5e5e5;
    background: #fff;
    color: #333;
    text-decoration: none;
    transition: all 0.3s;
}

.pagination a:hover {
    background: #f5f5f5;
    color: #EE3131;
    border-color: #EE3131;
}

.pagination span.current {
    background: #EE3131;
    color: #fff;
    border-color: #EE3131;
}