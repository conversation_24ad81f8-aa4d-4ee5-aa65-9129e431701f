# PC端图片预览切换箭头位置优化

## 问题描述

原来的PC端信息详情页面图片预览功能存在用户体验问题：
- 预览图会根据图片大小进行自适应显示
- 切换箭头的位置随着图片大小变化而移动
- 用户在切换图片时需要重新定位箭头位置，体验不佳

## 解决方案

### 1. 切换箭头位置固定化
- **原来**：切换箭头相对于 `.lightbox-container` 定位，位置随容器大小变化
- **现在**：切换箭头相对于整个视窗（`.lightbox-overlay`）定位，位置固定在屏幕两侧

### 2. 样式优化
- 箭头使用 `position: fixed` 定位
- 固定在视窗左侧30px和右侧30px位置
- 增加了悬停效果和缩放动画
- 添加了响应式设计，在小屏幕上调整箭头大小和位置

### 3. 智能显示逻辑
- 当只有一张图片时，自动隐藏切换箭头
- 多张图片时正常显示切换箭头

## 修改的文件

### 1. CSS样式文件
**文件**: `template/pc/css/view.css`

```css
/* 切换箭头固定在视窗两侧，不随图片大小变化位置 */
.lightbox-prev, .lightbox-next { 
    position: fixed; 
    top: 50%; 
    transform: translateY(-50%); 
    /* ... 其他样式 */ 
}

/* 只有一张图片时隐藏切换箭头 */
.lightbox-overlay.single-image .lightbox-prev,
.lightbox-overlay.single-image .lightbox-next { 
    display: none; 
}

/* 响应式设计 */
@media (max-width: 768px) {
    .lightbox-prev { left: 15px; width: 45px; height: 45px; font-size: 24px; }
    .lightbox-next { right: 15px; width: 45px; height: 45px; font-size: 24px; }
}
```

### 2. HTML结构调整
**文件**: `template/pc/view.htm` 和 `template/pc/view_fang.htm`

```html
<!-- 原来：箭头在容器内 -->
<div class="lightbox-container">
    <!-- 内容 -->
    <a class="lightbox-prev">&#10094;</a>
    <a class="lightbox-next">&#10095;</a>
</div>

<!-- 现在：箭头在overlay层 -->
<div class="lightbox-container">
    <!-- 内容 -->
</div>
<a class="lightbox-prev">&#10094;</a>
<a class="lightbox-next">&#10095;</a>
```

### 3. JavaScript逻辑增强
**文件**: `template/pc/view.htm` 和 `template/pc/view_fang.htm`

```javascript
// 根据图片数量决定是否显示切换箭头
if (galleryImages.length <= 1) {
    lightbox.classList.add('single-image');
} else {
    lightbox.classList.remove('single-image');
}
```

## 测试文件

创建了测试页面 `template/pc/lightbox_test.html` 用于验证优化效果：
- 包含不同尺寸的图片测试用例
- 包含单张图片和多张图片的测试场景
- 可以直观地验证切换箭头位置是否固定

## 优化效果

### 用户体验改进
1. **位置一致性**：切换箭头始终固定在屏幕两侧，用户无需重新定位
2. **视觉清晰**：箭头样式更加明显，增加了边框和悬停效果
3. **智能隐藏**：单张图片时自动隐藏无用的切换箭头
4. **响应式适配**：在移动设备上自动调整箭头大小和位置

### 技术改进
1. **代码结构优化**：将箭头从容器内移到overlay层，逻辑更清晰
2. **CSS组织优化**：使用CSS类控制显示状态，便于维护
3. **兼容性保持**：保持原有的键盘导航和点击事件功能

## 使用说明

优化后的功能完全兼容原有的使用方式：
- 点击图片打开预览
- 使用箭头或键盘左右键切换图片
- 点击背景或ESC键关闭预览
- 支持图片数量显示和标题显示

## 浏览器兼容性

- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+
- IE 11+（部分CSS3特性可能有差异）

## 后续维护

如需进一步调整箭头位置或样式，只需修改 `template/pc/css/view.css` 中的相关CSS变量即可。
