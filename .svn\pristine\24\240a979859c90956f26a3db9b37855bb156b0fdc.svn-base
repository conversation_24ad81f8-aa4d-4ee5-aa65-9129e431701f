<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title><?php echo $page_title; ?> - <?php echo $site_name; ?></title>
    <meta name="keywords" content="<?php echo $site_name; ?>,新闻中心,新闻资讯,本地新闻" />
    <meta name="description" content="<?php echo $site_name; ?>新闻中心，提供本地最新新闻资讯。" />
    <link rel="stylesheet" href="/template/pc/css/common.css?<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/pc/css/news.css?<?php echo time(); ?>">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
</head>
<body>
        <!-- 顶部 -->
	<div class="yui-top  yui-1200">
		<div class="yui-top-center">
			<div class="yui-top-left yui-left">
				<a href="https://www.botou.net/">1网站首页</a>
				<a href="#">移动版</a>
				<a href="#">微信公众号</a>
				<a href="#">快速发布</a>
			</div>

			<div class="yui-top-right yui-right yui-text-right">
				<a href="#">登录</a><a href="#">注册</a><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">会员中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">我的信息</a></li>
						<li><a href="#">我的收藏</a></li>
						<li><a href="#">账号设置</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">商家中心</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">商家入驻</a></li>
						<li><a href="#">商家管理</a></li>
						<li><a href="#">营销推广</a></li>
					</ul>
				</div><div class="yui-top-dropdown">
					<span class="yui-top-dropdown-btn">网站导航</span>
					<ul class="yui-top-dropdown-menu">
						<li><a href="#">关于我们</a></li>
						<li><a href="#">联系我们</a></li>
						<li><a href="#">使用帮助</a></li>
					</ul>
				</div>
			</div>
		</div>
	</div>
        <!-- 页面切换导航 -->
        <!-- <div class="page-switch-nav">
            <div class="yui-1200">
                <a href="index.htm" class="active">首页</a>
                <a href="list.htm">列表页</a>
                <a href="view.htm">详情页</a>
            </div>
        </div> -->
	<!-- header-->
	<div class="yui-header yui-1200">

		<div class="yui-t yui-c-box">
			<div class="yui-logo">
				<a href="https://www.botou.net/"><img src="/template/pc/images/logo.png" alt="泊头生活网" srcset=""></a>
			</div>
			<div class="yui-cimg"></div>
			<!--form select -->
			<div class="yui-form">
				<div class="yui-select">
					<!-- <div class="mod_select">
						<div class="select_box">
							<span class="select_txt">信息</span>
							<span class="select-icon"></span>
							<ul class="option">
								<li>信息</li>
								<li>帖子</li>

							</ul>
						</div>
					</div> -->
					<form action="/search.php" method="get" id="header-search-form">

						<input type="hidden" name="show" value="title" />
						<input type="hidden" name="tempid" value="1" />
						<input type="hidden" name="tbname" value="info">
						<input type="text" name="keyword"  class="import" placeholder="请输入关键字" id="header-search-input">
						<input type="submit" class="btn-search" id="header-search-btn" value="搜   索">
					</form>
				</div>
				<div class="yui-select-bottom-text"></div>
			</div>
			<div class="yui-fabu" style="float:right;">
				<button onClick="location.href='/post.php'"><a href="/post.php" target="_blank">免费发布信息</a></button>
			</div>
			<!-- form end -->
		</div>
	</div>
	<div class="yui-clear"></div>
	<div class="yui-nav mt20  yui-1200">
		<ul>
			<li <?php if(!null !== ($current_page ?? null) || $current_page == 'index'): ?>class='nav-cur'<?php endif; ?>><a href="/">首页</a></li>
			<?php 
			// 直接从数据库获取分类数据
			$categories = getCategories();
			
			// 筛选一级分类并排序
			$topCategories = array();
			foreach ($categories as $cat) {
				if ($cat['parent_id'] == 0 && $cat['status'] == 1) {
					$topCategories[] = $cat;
				}
			}
			
			// 按排序值升序排列
			usort($topCategories, function($a, $b) {
				if ($a['sort_order'] == $b['sort_order']) {
					return $a['id'] - $b['id']; // 如果排序值相同，按ID升序
				}
				return $a['sort_order'] - $b['sort_order']; // 按排序值升序
			});
			
			// 输出导航菜单
			foreach ($topCategories as $cat) {
				echo '<li><a href="/'.$cat['pinyin'].'/">'.$cat['name'].'</a></li>';
			}
			 ?>
			<li <?php if(null !== ($current_page ?? null) && $current_page == 'news'): ?>class='nav-cur'<?php endif; ?>><a href="/news.php">新闻中心</a></li>
		</ul>
	</div>

	<script>
	// Header搜索加载状态管理 - 使用多种方式确保兼容性
	(function() {
		function initHeaderSearch() {
			var headerSearchForm = document.getElementById('header-search-form');
			if (headerSearchForm) {
				headerSearchForm.addEventListener('submit', function(e) {
					var input = document.getElementById('header-search-input');
					var keyword = input ? input.value.trim() : '';

					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			}
		}

		function showHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜索中...';
				searchBtn.disabled = true;
				searchBtn.style.backgroundColor = '#6c757d';
				searchBtn.style.cursor = 'not-allowed';

				// 添加调试信息
				console.log('Header搜索加载状态已激活');
			}
		}

		function hideHeaderSearchLoading() {
			var searchBtn = document.getElementById('header-search-btn');

			if (searchBtn) {
				searchBtn.value = '搜   索';
				searchBtn.disabled = false;
				searchBtn.style.backgroundColor = '#3092d5';
				searchBtn.style.cursor = 'pointer';
			}
		}

		// 多种初始化方式确保兼容性
		if (document.readyState === 'loading') {
			document.addEventListener('DOMContentLoaded', initHeaderSearch);
		} else {
			initHeaderSearch();
		}

		// 如果有jQuery，也用jQuery方式绑定
		if (typeof $ !== 'undefined') {
			$(document).ready(function() {
				$('#header-search-form').on('submit', function(e) {
					var keyword = $('#header-search-input').val().trim();
					if (keyword) {
						showHeaderSearchLoading();
					}
				});
			});
		}

		// 暴露函数到全局作用域，方便调试
		window.showHeaderSearchLoading = showHeaderSearchLoading;
		window.hideHeaderSearchLoading = hideHeaderSearchLoading;
	})();
	</script>


    <div class="yui-clear"></div>
 
    <!-- 面包屑导航 -->
    <div class="yui-1200">
        <div class="breadcrumb-container">
            <div class="breadcrumb">
                <a href="/">首页</a>
                <span class="separator">></span>
                <span><?php echo $page_title ?? ""; ?></span>
            </div>
        </div>
    </div>

        
    <div class="yui-content yui-1200">
        <div class="news-container">
            <!-- 左侧主体区域 -->
            <div class="news-left">
                <!-- 新闻焦点 -->
                <div class="news-focus">
                    <div class="news-title">
                        <h3>新闻焦点</h3>
                    </div>
                    <div class="focus-content">
                        <?php
                        if (isset($recommend_news) && !empty($recommend_news) && is_array($recommend_news)) {
                            // 焦点图
                            echo '<div class="focus-main">';
                            echo '<a href="news.php?id=' . $recommend_news[0]['id'] . '">';
                            $thumb = empty($recommend_news[0]['thumb']) ? '/template/pc/images/no_image.jpg' : $recommend_news[0]['thumb'];
                            echo '<img src="' . $thumb . '" alt="' . $recommend_news[0]['title'] . '">';
                            echo '<div class="focus-main-title">';
                            echo '<h3>' . $recommend_news[0]['title'] . '</h3>';
                            echo '</div>';
                            echo '</a>';
                            echo '</div>';
                            
                            // 焦点列表
                            echo '<div class="focus-list">';
                            foreach ($recommend_news as $key => $news) {
                                if ($key > 0 && $key < 5) {
                                    echo '<div class="focus-item">';
                                    echo '<a href="news.php?id=' . $news['id'] . '">' . $news['title'] . '</a>';
                                    if (!empty($news['is_top'])) echo '<span class="top-tag">置顶</span>';
                                    if (!empty($news['is_recommend'])) echo '<span class="rec-tag">推荐</span>';
                                    echo '<div class="item-meta">';
                                    echo '<span>' . date('Y-m-d', $news['addtime']) . '</span>';
                                    if (!empty($news['author'])) {
                                        echo '<span>' . $news['author'] . '</span>';
                                    }
                                    echo '</div>';
                                    echo '</div>';
                                }
                            }
                            echo '</div>';
                        }
                        ?>
                    </div>
                </div>
                
                <!-- 栏目新闻 -->
                <?php if($top_categories): foreach($top_categories as $cat_id => $cat): ?>
                <div class="news-block">
                    <div class="news-title">
                        <h3><?php echo $cat['catname']; ?></h3>
                        <a href="news.php?catid=<?php echo $cat_id; ?>" class="more">更多</a>
                    </div>
                    <div class="news-list">
                        <?php if($cat_news[$cat_id]): foreach($cat_news[$cat_id] as $key => $news): ?>
                        <?php if($key == 0): ?>
                        <!-- 头条新闻 -->
                        <div class="headline-news">
                            <?php if(!empty($news['thumb'])): ?>
                            <div class="headline-img">
                                <a href="news.php?id=<?php echo $news['id']; ?>">
                                    <img src="<?php echo $news['thumb']; ?>" alt="<?php echo $news['title']; ?>">
                                </a>
                            </div>
                            <?php endif; ?>
                            <div class="headline-info">
                                <h3><a href="news.php?id=<?php echo $news['id']; ?>"><?php echo $news['title']; ?></a>
                                <?php if(!empty($news['is_top'])): ?><span class="top-tag">置顶</span><?php endif; ?>
                                <?php if(!empty($news['is_recommend'])): ?><span class="rec-tag">推荐</span><?php endif; ?>
                                </h3>
                                <div class="item-meta">
                                    <span><?php echo date('Y-m-d', $news['addtime']); ?></span>
                                    <span><?php echo $news['catname']; ?></span>
                                </div>
                                <div class="headline-desc">
                                    <?php 
                                    if (!empty($news['description'])) {
                                        echo cut_str($news['description'], 100);
                                    }
                                    ?>
                                </div>
                            </div>
                        </div>
                        <?php else: ?>
                        <!-- 列表新闻 -->
                        <div class="news-item">
                            <h3><a href="news.php?id=<?php echo $news['id']; ?>"><?php echo $news['title']; ?></a>
                            <?php if(!empty($news['is_top'])): ?><span class="top-tag">置顶</span><?php endif; ?>
                            <?php if(!empty($news['is_recommend'])): ?><span class="rec-tag">推荐</span><?php endif; ?>
                            </h3>
                            <div class="item-meta">
                                <span><?php echo date('Y-m-d', $news['addtime']); ?></span>
                                <?php if(!empty($news['author'])): ?>
                                <span><?php echo $news['author']; ?></span>
                                <?php endif; ?>
                            </div>
                        </div>
                        <?php endif; ?>
                        <?php endforeach; endif; ?>
                    </div>
                </div>
                <?php endforeach; endif; ?>
            </div>
            
            <!-- 右侧边栏 -->
            <!-- 右侧边栏 -->
<div class="news-right">
    <div class="bbs-hot">
        <div class="yui-h-title">
            <h3>公益广告</h3><span></span>
        </div>
        <div class="yui-img-list">
            <a href=""><img src="/template/pc/images/16382506823782.png" /></a>
        </div>
    </div>
 </div> 
        </div>
    </div>
</div>
    <div class="yui-footer">
    <div class="yui-1200">
        <div class="footer-content bg-white">
            <p class="footer-nav">
                <a href="https://www.botou.net/" title="泊头生活网">网站首页</a>
                <a href="https://www.botou.net/aboutus/tuiguang.html" target="_blank">广告服务</a>
                <a href="https://www.botou.net/aboutus/shenmin.html" target="_blank">法律声明</a>
                <a href="https://www.botou.net/aboutus/about.html" target="_blank">网站介绍</a>
                <a href="https://www.botou.net/aboutus/contactus.html" target="_blank">联系我们</a>
                <a href="https://www.botou.net/aboutus/job.html" target="_blank">招聘信息</a>
            </p>
            <p class="footer-disclaimer">2本站信息均由网民发表,不代表本网站立场,如侵犯了您的权利请致电投诉</p>
            <p class="footer-disclaimer">客服电话： &nbsp; 客服邮箱：<font><EMAIL></font> <a href="http://cyberpolice.mps.gov.cn/wfjb/" target="_blank" rel="nofollow">网络违法犯罪举报网站</a></p>
            <p class="footer-copyright">Copyright © 2006-2023 <a href="https://www.botou.net" title="泊头生活网" class="db_link">泊头生活网</a> 版权所有</p>
            <p class="footer-copyright"><a href="https://beian.miit.gov.cn/" target="_blank" id="footericp" rel="nofollow">冀ICP备2023009391号-1</a> <a rel="nofollow" href="http://www.beian.gov.cn/portal/registerSystemInfo?recordcode=13098102000307">冀公网安备 13098102000307号</a></p>
        </div>
    </div>
</div>

    <script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
    <script type="text/javascript" src="/template/pc/js/common.js"></script>
</body>
</html> 