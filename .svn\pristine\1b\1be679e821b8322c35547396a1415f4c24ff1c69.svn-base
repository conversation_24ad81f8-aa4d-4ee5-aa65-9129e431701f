{include file="header.htm"}

<!-- 页面标题 -->
<div class="page-title">
    <h1>系统设置</h1>
    <div class="d-flex gap-2">
        <a href="index.php" class="btn btn-outline">
            <i class="fas fa-arrow-left"></i>
            <span>返回首页</span>
        </a>
    </div>
</div>

<!-- 内容区域 -->
<div class="section">
    <!-- 消息提示 -->
    {if !empty($message)}
    <div class="alert alert-success">
        <i class="fas fa-check-circle"></i>
        <div>
            <p>{$message}</p>
        </div>
    </div>
    {/if}

    {if !empty($debug_info)}
    <div class="alert alert-info">
        <i class="fas fa-info-circle"></i>
        <div>
            <p>调试信息</p>
            <div>{$debug_info}</div>
        </div>
    </div>
    {/if}

    <!-- 设置选项卡 -->
    <div class="setting-tabs">
        {foreach $groups as $group_key => $group_name}
        <a href="?group={$group_key}" class="setting-tab{if $current_group == $group_key} active{/if}">
            {$group_name}
        </a>
        {/foreach}
    </div>

    <!-- 设置表单 -->
    <div class="card">
        <form method="post" action="" class="form-horizontal">
            <input type="hidden" name="action" value="save_settings">
            <input type="hidden" name="group" value="{$current_group}">

            {foreach $settings as $setting}
            <div class="form-group">
                <label class="form-label" for="{$setting.setting_key}">{$setting.setting_title}</label>

                <div class="form-field">
                    {if $setting.setting_type == 'text'}
                    <input type="text" id="{$setting.setting_key}" name="{$setting.setting_key}" value="{$setting.setting_value}" class="form-control" placeholder="请输入{$setting.setting_title}">

                    {elseif $setting.setting_type == 'number'}
                    <input type="number" id="{$setting.setting_key}" name="{$setting.setting_key}" value="{$setting.setting_value}" class="form-control" min="1" step="1" placeholder="请输入数字">

                    {elseif $setting.setting_type == 'textarea'}
                    <textarea id="{$setting.setting_key}" name="{$setting.setting_key}" class="form-textarea" placeholder="请输入{$setting.setting_title}">{$setting.setting_value}</textarea>

                    {elseif $setting.setting_type == 'radio' || $setting.setting_type == 'switch'}
                    <div class="d-flex gap-3">
                        <div class="form-check">
                            <input type="radio" name="{$setting.setting_key}" id="{$setting.setting_key}_1" value="1" {if $setting.setting_value == '1'}checked{/if}>
                            <label class="form-check-label" for="{$setting.setting_key}_1">启用</label>
                        </div>
                        <div class="form-check">
                            <input type="radio" name="{$setting.setting_key}" id="{$setting.setting_key}_0" value="0" {if $setting.setting_value == '0'}checked{/if}>
                            <label class="form-check-label" for="{$setting.setting_key}_0">禁用</label>
                        </div>
                    </div>

                    {elseif $setting.setting_type == 'select'}
                    <select id="{$setting.setting_key}" name="{$setting.setting_key}" class="form-select">
                        {if $setting.setting_key == 'mobile_pagination_mode'}
                        <option value="pagination" {if $setting.setting_value == 'pagination'}selected{/if}>传统分页模式（上一页/下一页）</option>
                        <option value="loadmore" {if $setting.setting_value == 'loadmore'}selected{/if}>点击加载更多模式</option>
                        <option value="infinite" {if $setting.setting_value == 'infinite'}selected{/if}>滚动无限加载模式</option>
                        {else}
                        {assign var=options value=json_decode($setting.setting_options, true)}
                        {foreach $options as $option_key => $option_value}
                        <option value="{$option_key}" {if $setting.setting_value == $option_key}selected{/if}>{$option_value}</option>
                        {/foreach}
                        {/if}
                    </select>

                    {elseif $setting.setting_type == 'checkbox'}
                    <div class="d-flex flex-wrap gap-3">
                        {assign var=options value=json_decode($setting.setting_options, true)}
                        {assign var=selected_values value=explode(',', $setting.setting_value)}
                        {foreach $options as $option_key => $option_value}
                        <div class="form-check">
                            <input type="checkbox" name="{$setting.setting_key}[]" id="{$setting.setting_key}_{$option_key}" value="{$option_key}" {if in_array($option_key, $selected_values)}checked{/if}>
                            <label class="form-check-label" for="{$setting.setting_key}_{$option_key}">{$option_value}</label>
                        </div>
                        {/foreach}
                    </div>
                    {/if}
                </div>

                {if !empty($setting.setting_description)}
                <div class="form-help">
                    <span class="help-text">{$setting.setting_description}</span>
                </div>
                {/if}
            </div>
            {/foreach}

            <!-- 提交按钮 -->
            <div class="form-actions">
                <button type="submit" class="btn btn-primary">
                    <i class="fas fa-save"></i>
                    <span>保存设置</span>
                </button>
                <button type="reset" class="btn btn-outline">
                    <i class="fas fa-undo"></i>
                    <span>重置</span>
                </button>
            </div>
        </form>
    </div>
</div>

{include file="footer.htm"}