<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8">
    <title>2222{$seo_title}</title>
    <meta name="keywords" content="{if $category.seo_keywords}{$category.seo_keywords}{else}{$category.name}{/if}">
    <meta name="description" content="{if $category.seo_description}{$category.seo_description}{else}{$category.description}{/if}">
		<link rel="stylesheet" href="/template/pc/css/common.css">
    <link rel="stylesheet" href="/template/pc/css/category.css">
    <link rel="stylesheet" href="/template/pc/css/filter.css">
    <script type="text/javascript" src="/template/pc/js/m.js"></script>
	</head>
	<body>
		{include file="header.htm"}
<div class="yui-clear"></div>

<!-- 主内容区域 -->
<div class="yui-1200">
    <!-- 面包屑导航 -->
    <div class="breadcrumb-container">
        <div class="breadcrumb">
            <a href="/">首页</a>
            <span class="separator">></span>
            {if $category.parent_id > 0 && $parent_category}
            <a href="/{$parent_category.pinyin}/">{$parent_category.name}</a>
            <span class="separator">></span>
            {/if}
            
            <a href="/{$category.pinyin}/">{$category.name}</a>
        </div>
    </div>
</div>

<!-- 栏目列表筛选区 -->
<div class="yui-content  yui-1200">
    <div class="filter-container">
        <div class="filter-section">
            <div class="filter-label">分类：</div>
            <div class="filter-options">
				{if $current_is_subcategory}
				<a href="{$parentUrl}" class="filter-item {if $category.id == $parent_category.id}active{/if}">全部<span>(1)</span></a>
				{else}
				<a href="{$baseUrl}" class="filter-item active">全部</a>
				{/if}
				
			    {loop $subCategories $subCat}
                <a href="{$subCat.url}" class="filter-item {if (isset($subCat.is_current) && $subCat.is_current) || (isset($current_subcategory_id) && isset($subCat.id) && $current_subcategory_id == $subCat.id)}active{/if}">{$subCat.name}<span>(1)</span></a>
				{/loop}
              
            </div>
        </div>
        <div class="filter-section">
            <div class="filter-label">地区：</div>
            <div class="filter-options">
			{if $showCities}
				{loop $area_arr $area}
					{if isset($area.is_unlimited) && $area.is_unlimited}
					<a href="{$area.url}" class="filter-item">不限</a>
					{elseif isset($area.is_city) && $area.is_city}
					<a href="{$area.url}" class="filter-item {if $areaId == $area.id}active{/if}">{$area.name}</a>
					{/if}
				{/loop}
			{else}
				{loop $area_arr $area}
					{if isset($area.is_unlimited) && $area.is_unlimited}
					<a href="{$area.url}" class="filter-item {if $areaId == 0}active{/if}">全部</a>
					{elseif isset($area.is_province) && $area.is_province}
					<a href="{$area.url}" class="filter-item {if $areaId == $area.id}active{/if}">{$area.name}</a>
					{/if}
				{/loop}
			{/if}
           </div>
        </div>
    </div>
</div>

<div class="yui-content yui-1200">
    <div class="yui-index-info-list">

				<!-- 列表左侧 -->
				<div class="yui-index-info-list-left">
					<div class="yui-list-tabs">
						<div class="tab-header">
							<ul>
								<li class="active">全部信息category_fang.htm</li>
								
							</ul>
							<span class="index-list-post yui-right"><a href="/post.php?cid={$category.id}">在{$category.name}分类下发布信息</a></span>
						</div>
						{if !empty($posts)}
						<div class="tab-content">
							<div class="tab-pane active">
								<div class="list-header">
									<div class="list-title">信息主题</div>
									<div class="list-meta">
										<span>地区</span>
										<span>有效期</span>
										<!-- <span>价格</span> -->
										<span>时间</span>
									</div>
								</div>
								<ul class="info-list">
									{loop $posts $post}
									{php}
									if (!empty($post['expired_at'])) {
										$days = getRemainingDaysInt($post['expired_at']);
										$guoqi = $days > 0 ? $days.'天' : '已过期';
									} else {
										$guoqi = '长期';
									}
									{/php}
									<li>
										<div class="info-title"> 
											
										<a {if $guoqi=='已过期'} style="text-decoration: line-through;"{/if} target="_blank" href="/{$post.category_pinyin}/{$post.id}.html">{$post.title}</a>{if $post.is_top_category}<span class="top-tag">顶</span>{/if}</div>
										<div class="info-meta">
											<span class="area">{$post.region_name}</span>
											<span class="validity">{$guoqi}</span>
											<!-- <span class="price">面议</span> -->
											<span class="time">{$post.updated_at|friendlyTime}</span>
										</div>
									</li>
									{/loop}
									</ul>
							</div>
						</div>
						{else}
						<div class="no-info-message" style="text-align: center;">
							<i class="no-info-icon"></i>
							<p>暂无相关信息</p>
						</div>
						{/if}
					</div>
					
					{if $totalPages > 1}
					{$pagination}
					{/if}


				</div>
				<!-- 列表左侧 end -->

				<!-- 列表右侧 -->
				<div class="yui-index-info-list-right">
					<div class="bbs-hot">
						<div class="yui-h-title">
							<h3>公益广告</h3><span></span>
						</div>
						<div class="yui-img-list">
              <a href=""><img src="/template/pc/images/16382506823782.png" /></a>
						</div>
					</div>
					<div class="bbs-hot">
						<div class="yui-h-title">
							<h3>最新信息</h3><span><a href="/">更多</a></span>
						</div>
						<div class="yui-small-list">
							<ul>
								<li><a href="/showinfo-61-42-0.html" target="_blank">泊头成人高考报名_工作取证两不误</a></li>
								<li><a href="/showinfo-18-1538-0.html" target="_blank">出租空地</a></li>
								<li><a href="/showinfo-28-1535-0.html" target="_blank">招聘工人</a></li>
								<li><a href="/showinfo-40-1533-0.html" target="_blank">手机/电脑</a></li>
								<li><a href="/showinfo-84-1532-0.html" target="_blank">手机/电脑全业务服务</a></li>
								<li><a href="/showinfo-19-1497-0.html" target="_blank">招聘美容师，人事经理</a></li>
								<li><a href="/showinfo-12-10-0.html" target="_blank">出售龙湾小区高层3室2厅1卫，豪华装修</a></li>
								<li><a href="/showinfo-74-1496-0.html" target="_blank">脉冲除尘器</a></li>
								<li><a href="/showinfo-93-1494-0.html" target="_blank">出自用小米120w氮化镓充电器</a></li>
								<li><a href="/showinfo-19-41-0.html" target="_blank">招聘美工，电话销售</a></li>
								<li><a href="/showinfo-13-38-0.html" target="_blank">泊头五金建材城西区出租三层门市</a></li>
								<li><a href="/showinfo-13-37-0.html" target="_blank">整体出租门市楼</a></li>
							</ul>
						</div>
					</div>
					
					<div class="bbs-hot">
						<div class="yui-h-title">
							<h3>便民服务</h3><span></span>
						</div>
						<div class="yui-small-list">
							<ul>
								<li><a href="#" target="_blank">泊头市公交线路</a></li>
								<li><a href="#" target="_blank">泊头天气预报</a></li>
								<li><a href="#" target="_blank">泊头医院电话</a></li>
								<li><a href="#" target="_blank">泊头学校信息</a></li>
								<li><a href="#" target="_blank">泊头市政服务</a></li>
								<li><a href="#" target="_blank">泊头求职招聘</a></li>
							</ul>
						</div>
					</div>
				</div>
				<!-- 列表右侧 end -->
			</div>

			<!-- 栏目列表end  -->
    </div>
</div>

{include file="footer.htm"}
		<!-- 主区域  end-->
	
		<script type="text/javascript" src="/template/pc/js/jquery.min.js"></script>
		<script type="text/javascript" src="/template/pc/js/common.js"></script>
		<script>
			$(function() {
				// 筛选标签选择功能
				$('.filter-tags .filter-item').click(function(e) {
					e.preventDefault();
					$(this).toggleClass('active');
				});

				// 重置筛选按钮
				$('.filter-reset').click(function() {
					$('.filter-options .filter-item').removeClass('active');
					$('.filter-options .filter-item').first().addClass('active');
					$('.filter-tags .filter-item').removeClass('active');
				});

				// 确定筛选按钮
				$('.filter-submit').click(function() {
					alert('筛选条件已应用');
				});

				// 初始化加载更多功能
				initLoadMoreButton();
			})

			// 是否正在加载中的标志
			var isLoading = false;

			// 初始化加载更多按钮
			function initLoadMoreButton() {
				$('.load-more').on('click', function() {
					if (isLoading) return;

					const nextPage = parseInt($(this).data('next-page'));
					const catId = parseInt($(this).data('cat-id'));
					const areaId = parseInt($(this).data('area-id'));

					loadMorePosts(nextPage, catId, areaId);
				});
			}

			// 加载更多文章函数
			function loadMorePosts(page, catId, areaId) {
				if (isLoading) return;

				const loadMoreBtn = $('.load-more');
				if (!loadMoreBtn.length) return;

				isLoading = true;
				loadMoreBtn.addClass('loading');
				loadMoreBtn.text('加载中...');

				// 构建URL
				const url = `/api/posts_loadmore.php?cat_id=${catId}&page=${page}&area_id=${areaId}`;
				console.log('正在加载URL:', url);

				// 使用jQuery的AJAX
				$.ajax({
					url: url,
					type: 'GET',
					dataType: 'json',
					success: function(data) {
						isLoading = false;
						console.log('收到数据:', data);

						if (data.code === 200) {
							// 直接将HTML内容添加到列表中
							const html = data.html;
							$('.info-list').append(html);
							console.log('添加了新内容');

							// 更新加载更多按钮
							if (data.has_more) {
								loadMoreBtn.removeClass('loading');
								loadMoreBtn.text('点击加载更多');
								loadMoreBtn.data('next-page', data.next_page);
								console.log('设置下一页:', data.next_page);
							} else {
								// 没有更多内容
								const container = loadMoreBtn.closest('.pagination-container');
								if (container.length) {
									container.html('<div class="load-more-end">已加载全部内容</div>');
								} else {
									loadMoreBtn.parent().html('<div class="load-more-end">已加载全部内容</div>');
								}
								console.log('没有更多内容');
							}
						} else {
							// 加载失败
							loadMoreBtn.removeClass('loading');
							loadMoreBtn.text('加载失败，点击重试');
							console.error('加载失败:', data.msg);
						}
					},
					error: function(xhr, status, error) {
						isLoading = false;
						loadMoreBtn.removeClass('loading');
						loadMoreBtn.text('加载失败，点击重试');
						console.error('加载更多内容失败:', error);
					}
				});
			}
		</script>
	</body>
</html>
