<!DOCTYPE html>
{php}
// 获取主题设置
$theme = isset($_COOKIE['site_theme']) ? $_COOKIE['site_theme'] : 'red';
$theme_class = 'theme-' . $theme;
{/php}
<html lang="zh-CN" class="{$theme_class} no-fouc">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
    <title>{$post.title} - {$site_name}</title>
    <meta name="keywords" content="{$post.title},{$post.category_name},{$site_name}">
    <meta name="description" content="{php}echo mb_substr(strip_tags($post['content']), 0, 100, 'utf-8');{/php}">
    <link rel="stylesheet" href="/static/font-awesome/css/all.min.css">
    <link rel="stylesheet" href="/static/css/themes.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/common.css?v=<?php echo time(); ?>">
    <link rel="stylesheet" href="/template/m/css/view.css?v=<?php echo time(); ?>">
    <script src="/static/js/common.js"></script>
    <script src="/static/js/page-stabilizer.js"></script>
    <style>
        /* 移除区域标题左侧边框 */
        .post-gallery-title:before,
        .post-content-title:before,
        .post-fields-title:before,
        .contact-title:before,
        .section-title:before {
            display: none !important;
        }

        /* 相应地移除左侧内边距并减小字体粗细 */
        .post-gallery-title,
        .post-content-title,
        .post-fields-title,
        .contact-title,
        .section-title {
            padding-left: 0 !important;
            font-weight: 500 !important;
            font-size: 16px !important;
        }

        /* 详情页元数据样式 - 内联确保优先级 */
        .detail-meta {
            margin-bottom: 15px !important;
            font-size: 12px !important;
            color: #999 !important;
        }

        .detail-meta .meta-row {
            display: flex !important;
            flex-wrap: wrap !important;
            gap: 12px !important;
            margin-bottom: 8px !important;
        }

        .detail-meta .meta-row:last-child {
            margin-bottom: 0 !important;
        }

        .detail-meta .meta-item {
            display: flex !important;
            align-items: center !important;
            white-space: nowrap !important;
        }

        .detail-meta .meta-item i {
            margin-right: 4px !important;
            font-size: 11px !important;
            width: 12px !important;
            text-align: center !important;
        }

        /* 图标颜色 */
        .detail-meta .meta-row:first-child .meta-item:first-child i {
            color: #ff6b6b !important; /* 地区图标 */
        }

        .detail-meta .meta-row:first-child .meta-item:nth-child(2) i {
            color: #4ecdc4 !important; /* 有效期图标 */
        }

        .detail-meta .meta-row:first-child .meta-item:last-child i {
            color: #45b7d1 !important; /* 时间图标 */
        }

        .detail-meta .meta-row:last-child .meta-item:first-child i {
            color: #28a745 !important; /* 浏览图标 */
        }

        .detail-meta .meta-row:last-child .meta-item:nth-child(2) i {
            color: #6c757d !important; /* 编号图标 */
        }

        .detail-meta .report-link {
            color: #dc3545 !important;
            text-decoration: none !important;
        }

        .detail-meta .report-link i {
            color: #dc3545 !important;
        }

        /* 联系信息样式 */
        .contact-meta-item {
            display: flex !important;
            align-items: center !important;
            margin-bottom: 12px !important;
            font-size: 14px !important;
        }

        .contact-meta-item:last-child {
            margin-bottom: 0 !important;
        }

        .contact-meta-item i {
            width: 16px !important;
            text-align: center !important;
            margin-right: 8px !important;
            font-size: 14px !important;
        }

        .contact-meta-item .fas.fa-user {
            color: #17a2b8 !important; /* 用户图标 */
        }

        .contact-meta-item .fas.fa-phone {
            color: #28a745 !important; /* 电话图标 */
        }

        .contact-meta-item .fas.fa-map-marker-alt {
            color: #ff6b6b !important; /* 地址图标 */
        }

        .contact-meta-item .fab.fa-weixin {
            color: #07c160 !important; /* 微信图标 */
        }

        .contact-meta-item .contact-label {
            min-width: 50px !important;
            color: #999 !important;
            font-weight: 500 !important;
            margin-right: 8px !important;
        }

        .contact-meta-item .contact-value {
            flex: 1 !important;
            color: #333 !important;
            word-break: break-all !important;
        }

        .contact-meta-item .phone-number {
            font-size: 16px !important;
            font-weight: bold !important;
            color: #ff6600 !important;
        }

        .contact-meta-item .expired-text {
            font-size: 14px !important;
            color: #999 !important;
            font-style: italic !important;
        }
        
        /* 减小内容标题的字体粗细 */
        .post-title {
            font-weight: 500 !important;
            font-size: 20px !important;
        }
        
        /* 减淡信息编号、浏览次数和发布时间的文字颜色 */
        .post-id, .post-time, .post-views {
            color: #aaaaaa !important;
            font-size: 13px !important;
        }
        
        /* 文本内容样式调整 */
        .post-text {
            font-size: 16px !important;
            color: #333333 !important;
            line-height: 1.8 !important;
        }
        
        /* 区域与栏目标签样式 */
        .tag-label {
            display: inline-block;
            padding: 3px 8px;
            border-radius: 3px;
            font-size: 12px;
            font-weight: normal;
            margin-right: 6px;
            color: #5d8cb9;
            background-color: #eef4fb;
            border: 1px solid #d7e6f5;
        }
        
        /* 不同标签使用不同颜色 */
        .tag-label.parent-category {
            color: #5c7cb4;
            background-color: #e8f0fd;
            border: 1px solid #d0e0f5;
        }
        
        .tag-label.category {
            color: #5d8cb9;
            background-color: #eef4fb;
            border: 1px solid #d7e6f5;
        }
        
        .tag-label.region {
            color: #6baac4;
            background-color: #edf7fa;
            border: 1px solid #d5ecf2;
        }
        
        /* 管理弹出层 */
        .manage-layer {
            position: fixed;
            left: 0;
            right: 0;
            bottom: 0;
            top: 0;
            background: rgba(0,0,0,0.65);
            z-index: 999;
            display: none;
            align-items: center;
            justify-content: center;
        }
        
        .manage-content-centered {
            width: 85%;
            max-width: 320px;
            background: #fff;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
            transform: scale(0.85);
            opacity: 0;
            transition: transform 0.3s, opacity 0.3s;
        }
        
        .manage-layer.active {
            display: flex;
        }
        
        .manage-layer.active .manage-content-centered {
            transform: scale(1);
            opacity: 1;
        }
        
        .password-dialog {
            padding: 20px;
        }
        
        .manage-title {
            font-size: 18px;
            color: #333;
            margin: 0 0 10px;
            text-align: center;
            font-weight: 600;
        }
        
        .manage-subtitle {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin: 0 0 20px;
        }
        
        .password-input-container {
            position: relative;
            margin-bottom: 20px;
        }
        
        .password-icon {
            position: absolute;
            left: 10px;
            top: 50%;
            transform: translateY(-50%);
            color: #999;
        }
        
        .manage-input {
            width: 100%;
            border: 1px solid #ddd;
            border-radius: 6px;
            padding: 12px 12px 12px 35px;
            margin-bottom: 5px;
            box-sizing: border-box;
            font-size: 14px;
            transition: border-color 0.3s;
        }
        
        .manage-input:focus {
            border-color: var(--primary-color);
            outline: none;
            box-shadow: 0 0 0 2px rgba(var(--primary-color-rgb), 0.1);
        }
        
        .manage-btn {
            display: block;
            width: 100%;
            border: none;
            background: var(--primary-color);
            color: #fff;
            padding: 12px 10px;
            border-radius: 6px;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 10px;
            cursor: pointer;
            box-shadow: 0 2px 6px rgba(var(--primary-color-rgb), 0.2);
            transition: all 0.2s;
        }
        
        .manage-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 4px 8px rgba(var(--primary-color-rgb), 0.25);
        }
        
        .manage-close {
            display: block;
            width: 100%;
            border: none;
            background: transparent;
            color: #666;
            padding: 10px;
            font-size: 14px;
            text-align: center;
            text-decoration: none;
            cursor: pointer;
        }
        
        /* Loading动画 */
        .loading {
            display: none;
            text-align: center;
            padding: 20px 0;
        }
        
        .loading i {
            animation: loading 1s linear infinite;
        }
        
        @keyframes loading {
            from { transform: rotate(0deg); }
            to { transform: rotate(360deg); }
        }
        
        /* 头部右侧按钮区域 */
        .header-right {
            display: flex;
            align-items: center;
            justify-content: flex-end;
        }
        
        /* 搜索图标按钮 */
        .header-search-icon {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 40px;
            height: 40px;
            color: white;
            border-radius: 50%;
        }
        
        .header-search-icon:active {
            background-color: rgba(255,255,255,0.1);
        }
        
        /* 搜索弹出层样式 */
        .search-layer {
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            background-color: var(--primary-color);
            transform: translateY(-100%);
            transition: transform 0.3s ease;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-layer.active {
            transform: translateY(0);
        }
        
        .search-header {
            display: flex;
            align-items: center;
            height: 50px;
            padding: 0 10px;
        }
        
        .search-back {
            width: 36px;
            height: 36px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #fff;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            margin-right: 10px;
            border: none;
            font-size: 14px;
            cursor: pointer;
        }
        
        .search-form {
            flex: 1;
            display: flex;
            align-items: center;
            background: rgba(255,255,255,0.2);
            border-radius: 18px;
            height: 36px;
            padding: 0 15px;
        }
        
        .search-icon {
            color: rgba(255,255,255,0.8);
            margin-right: 10px;
            font-size: 14px;
        }
        
        .search-input {
            flex: 1;
            border: none;
            background: transparent;
            color: #fff;
            font-size: 14px;
            outline: none;
            height: 100%;
        }
        
        .search-input::placeholder {
            color: rgba(255,255,255,0.7);
        }
        
        .search-cancel {
            margin-left: 10px;
            color: #fff;
            background: transparent;
            border: none;
            font-size: 14px;
            padding: 0 5px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <header>
        <div class="header-inner">
            <div class="header-left">
                <a href="javascript:history.back();" class="header-back">
                    <i class="fas fa-chevron-left"></i>
                </a>
            </div>
            <div class="header-title">信息详情</div>
            <div class="header-right">
                <a href="javascript:openSearchLayer();" class="header-search-icon">
                    <i class="fas fa-search"></i>
                </a>
            </div>
        </div>
    </header>

    <!-- 搜索弹出层 -->
    <div class="search-layer" id="searchLayer">
        <div class="search-header">
            <button class="search-back" onclick="closeSearchLayer()">
                <i class="fas fa-arrow-left"></i>
            </button>
            <form action="/search.php" method="get" class="search-form" id="view-search-form">
                <span class="search-icon" id="view-search-icon">
                    <i class="fas fa-search"></i>
                </span>
                <span class="search-loading" id="view-search-loading" style="display: none;">
                    <i class="fas fa-spinner fa-spin"></i>
                </span>
                <input type="text" name="keyword" class="search-input" placeholder="输入关键词搜索..." id="searchInput" autocomplete="off">
                <input type="hidden" name="category_id" value="{$post.category_id}">
            </form>
            <button class="search-cancel" onclick="closeSearchLayer()">取消</button>
        </div>
    </div>

    <div class="breadcrumb">
        <div class="container">
            <a href="/">首页</a>
            <span class="separator"></span>
            {if $post.parent_category_id > 0}
            <a href="/{$post.parent_category_pinyin}/">{$post.parent_category_name}</a>
            <span class="separator"></span>
            {/if}
            <a href="/{$post.category_pinyin}/">{$post.category_name}</a>
            <span class="separator"></span>
            <span class="current">详情</span>
        </div>
    </div>

    <div class="post-header skeleton-container">
        <div class="container">
            <h1 class="post-title {if empty($post.title)}skeleton{/if}">{$post.title}</h1>
            
            <!-- 添加标签区域 -->
            <div style="margin: 8px 0;">
                {if !empty($post.parent_category_name)}<span class="tag-label parent-category">{$post.parent_category_name}</span>{/if}
                {if !empty($post.category_name)}<span class="tag-label category">{$post.category_name}</span>{/if}
                {if !empty($post.region_name)}<span class="tag-label region">{$post.region_name}</span>{/if}
            </div>
            
            <!-- 详情页元数据 - 与列表页风格一致 -->
            <div class="detail-meta">
                <div class="meta-row">
                    <span class="meta-item">
                        <i class="fas fa-map-marker-alt"></i>
                        {$post.region_name|default:'未知地区'}
                    </span>
                    <span class="meta-item">
                        <i class="fas fa-calendar-alt"></i>
                        {php}
                        if (!empty($post['expired_at'])) {
                            $days = getRemainingDaysInt($post['expired_at']);
                            echo $days > 0 ? $days.'天' : '已过期';
                        } else {
                            echo '长期';
                        }
                        {/php}
                    </span>
                    <span class="meta-item">
                        <i class="far fa-clock"></i>
                        {php}echo friendlyTime($post['updated_at']);{/php}
                    </span>
                </div>
                <div class="meta-row">
                    <span class="meta-item">
                        <i class="fas fa-eye"></i>
                        {$post.view_count}次浏览
                    </span>
                    <span class="meta-item">
                        <i class="fas fa-hashtag"></i>
                        编号{$post.id}
                    </span>
                    <span class="meta-item">
                        <a href="javascript:void(0);" onclick="showReportModal({$post.id})" class="report-link">
                            <i class="fas fa-flag"></i>举报
                        </a>
                    </span>
                </div>
            </div>
            {if !empty($post.price) && $post.price > 0}
            <div class="post-price {if empty($post.price)}skeleton skeleton-text{/if}">￥{php}echo number_format($post['price'], 2);{/php}</div>
            {/if}
        </div>
    </div>

    <div class="post-content skeleton-container">
        <div style="position: relative; margin-top: 10px;">
            <div class="post-text {if empty($post.content)}skeleton skeleton-lines{/if}" id="post-content" style="max-height: 150px; overflow: hidden; transition: max-height 0.3s ease;">
                {$post.content}
            </div>
            <div id="content-fade" style="position: absolute; bottom: 0; left: 0; width: 100%; height: 40px; background: linear-gradient(to bottom, rgba(255,255,255,0), rgba(255,255,255,1)); display: none;"></div>
        </div>
        <div id="expand-btn" style="text-align: center; padding: 8px 0 0; color: var(--primary-color); display: none; cursor: pointer;">
            <span id="expand-text">查看全部</span> <i class="fas fa-chevron-down" id="expand-icon"></i>
        </div>

        {if !empty($images)}
        <div class="gallery-container" style="margin-top: 5px; display: flex; flex-wrap: wrap; gap: 4px;">
            {foreach $images as $key => $image}
            <div class="gallery-item {if empty($image.thumb_path)}skeleton{/if}" onclick="openLightbox({$key})">
                <img src="/{$image.thumb_path}" data-src="/{$image.thumb_path}" alt="{$post.title}" class="lazy-image">
            </div>
            {/foreach}
        </div>
        {/if}
    </div>

    {if !empty($post.fields)}
    <div class="post-fields skeleton-container">
        <div class="post-fields-title">附加信息</div>
        {foreach $post.fields as $field}
        <div class="field-item">
            <div class="field-label">{$field.label}:</div>
            <div class="field-value {if empty($field.value)}skeleton skeleton-text{/if}">{$field.value}</div>
        </div>
        {/foreach}
    </div>
    {/if}

    <div class="contact-box skeleton-container">
        <div class="contact-title">联系方式</div>
        {php}
        // 判断信息是否过期
        $isExpired = ($post['is_expired'] == 1);
        {/php}

        <!-- 联系信息 - 使用图标风格 -->
        <div class="contact-info-meta">
            <div class="contact-meta-item">
                <i class="fas fa-user"></i>
                <span class="contact-label">称呼:</span>
                <span class="contact-value {if empty($post.contact_name)}skeleton skeleton-text{/if}">{$post.contact_name}</span>
            </div>
            <div class="contact-meta-item">
                <i class="fas fa-phone"></i>
                <span class="contact-label">手机:</span>
                <span class="contact-value">
                    {if !$isExpired}
                        <span class="phone-number {if empty($post.mobile)}skeleton skeleton-text{/if}">{$post.mobile}</span>
                    {else}
                        <span class="expired-text">信息已过期，联系方式已隐藏</span>
                    {/if}
                </span>
            </div>
            {if !empty($post.contact_address)}
            <div class="contact-meta-item">
                <i class="fas fa-map-marker-alt"></i>
                <span class="contact-label">地址:</span>
                <span class="contact-value {if empty($post.contact_address)}skeleton skeleton-text{/if}">{$post.contact_address}</span>
            </div>
            {/if}
            {if !empty($post.wechat)}
            <div class="contact-meta-item">
                <i class="fab fa-weixin"></i>
                <span class="contact-label">微信:</span>
                <span class="contact-value {if empty($post.wechat)}skeleton skeleton-text{/if}">{$post.wechat}</span>
            </div>
            {/if}
        </div>

        {if !$isExpired}
        <div style="margin-top: 20px;">
            <a href="tel:{$post.mobile}" class="call-btn {if empty($post.mobile)}skeleton{/if}" style="display: block; background-color: var(--secondary-color); color: white; text-align: center; padding: 12px 0; font-size: 16px; font-weight: 600; text-decoration: none;">
                <i class="fas fa-phone-alt" style="margin-right: 8px;"></i>拨打电话
            </a>
        </div>
        {else}
        <div style="margin-top: 20px; padding: 15px; background-color: #f5f5f5; border-radius: 8px; text-align: center;">
            <div style="color: #999; font-size: 14px; margin-bottom: 8px;">
                <i class="fas fa-exclamation-triangle" style="margin-right: 5px;"></i>信息已过期
            </div>
            <div style="color: #666; font-size: 13px;">
                该信息已过期，联系方式暂不显示
            </div>
        </div>
        {/if}
        

        
      <div class="infoquote" style="margin-top: 15px; margin-bottom: 25px; background-color: #f8f8f8; border-radius: 4px; padding: 12px 15px; font-size: 12px; color: #555; line-height: 1.6; border: 1px solid #eaeaea;">
            <p><span class="t" style="display: block; font-weight: 600; color: #333; margin-bottom: 8px; font-size: 13px;">使用信息须知（必读）</span></p>
            <p style="margin: 0 0 5px;">①请认真阅读<a href="https://www.ycxinxi.com/agreemt" target="_blank" style="color: var(--primary-color);">服务协议</a>，使用信息视为已知晓并同意该协议</p>
            <p style="margin: 0 0 5px;">②该信息（含图片）由用户自行发布，其真实性、准确性和合法性由信息发布者负责</p>
            <p style="margin: 0 0 5px;">③永城信息港仅提供信息交流平台，不介入任何交易过程，不承担安全风险和法律责任</p>
            <p style="margin: 0 0 5px;">④强烈建议：拒绝预付费用、选择见面交易、核验证照资质、签订交易合同</p>
            <p style="margin: 0 0 5px;">⑤特别提示：提高警惕，谨防诈骗</p>
            <p style="margin: 0;">⑥信息举报投诉电话：<a href="tel:13019191901" style="color: var(--primary-color);">130-1919-1901</a>、<a href="tel:13019191902" style="color: var(--primary-color);">130-1919-1902</a></p>
        </div>
    </div>

    {if !empty($related_posts)}
    <!-- 相关信息部分已删除 -->
    {/if}

    <!-- 添加图片灯箱等现有脚本 -->
    {if !empty($images)}
    <div class="lightbox" id="lightbox">
        <img src="" alt="" class="lightbox-img" id="lightbox-img">
        <div class="lightbox-close" onclick="closeLightbox()"><i class="fas fa-times"></i></div>
        <div class="lightbox-prev" onclick="changeImage(-1)"><i class="fas fa-chevron-left"></i></div>
        <div class="lightbox-next" onclick="changeImage(1)"><i class="fas fa-chevron-right"></i></div>
    </div>

    <script>
        var images = [];
        {php}
        // 在PHP中生成JavaScript数组初始化
        echo "images = [";
        if (!empty($images)) {
            foreach ($images as $image) {
                if (!empty($image['file_path'])) {
                    echo '"/' . $image['file_path'] . '",';
                }
            }
        }
        echo "];";
        {/php}
        var currentImageIndex = 0;
        
        // 预加载图片，确保无论从哪个页面进入都能正确显示
        function preloadImages() {
            for (var i = 0; i < images.length; i++) {
                (function(i) {
                    var img = new Image();
                    img.onload = function() {
                        // 图片加载完成后更新对应展示图片的透明度
                        var galleryItems = document.querySelectorAll('.gallery-item img');
                        if (galleryItems[i]) {
                            galleryItems[i].style.opacity = '1';
                            galleryItems[i].classList.add('loaded');
                        }
                    };
                    img.src = images[i];
                })(i);
            }
        }
        
        function openLightbox(index) {
            currentImageIndex = index;
            document.getElementById('lightbox-img').src = images[index];
            document.getElementById('lightbox').style.display = 'block';
            document.body.style.overflow = 'hidden';
        }
        
        function closeLightbox() {
            document.getElementById('lightbox').style.display = 'none';
            document.body.style.overflow = 'auto';
        }
        
        function changeImage(step) {
            currentImageIndex = (currentImageIndex + step + images.length) % images.length;
            document.getElementById('lightbox-img').src = images[currentImageIndex];
        }
        
        // 点击图片以外的区域关闭灯箱
        document.getElementById('lightbox').addEventListener('click', function(e) {
            if (e.target === this) {
                closeLightbox();
            }
        });
        
        // 键盘控制
        document.addEventListener('keydown', function(e) {
            if (document.getElementById('lightbox').style.display === 'block') {
                if (e.key === 'Escape') {
                    closeLightbox();
                } else if (e.key === 'ArrowLeft') {
                    changeImage(-1);
                } else if (e.key === 'ArrowRight') {
                    changeImage(1);
                }
            }
        });
    </script>
    {/if}

    <!-- 内容展开收起脚本 -->
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            var content = document.getElementById('post-content');
            var fadeElement = document.getElementById('content-fade');
            var expandBtn = document.getElementById('expand-btn');
            var expandText = document.getElementById('expand-text');
            var expandIcon = document.getElementById('expand-icon');
            var expanded = false;
            
            // 检查内容是否足够长以需要展开按钮
            function checkContentHeight() {
                if (content.scrollHeight > 150) {
                    fadeElement.style.display = 'block';
                    expandBtn.style.display = 'block';
                }
            }
            
            // 展开/收起内容
            expandBtn.addEventListener('click', function() {
                if (!expanded) {
                    content.style.maxHeight = content.scrollHeight + 'px';
                    fadeElement.style.display = 'none';
                    expandText.textContent = '收起内容';
                    expandIcon.className = 'fas fa-chevron-up';
                    expanded = true;
                } else {
                    content.style.maxHeight = '150px';
                    fadeElement.style.display = 'block';
                    expandText.textContent = '查看全部';
                    expandIcon.className = 'fas fa-chevron-down';
                    expanded = false;
                }
            });
            
            // 初始检查
            checkContentHeight();
            
            // 骨架屏处理
            presetContentHeights();
            loadLazyImages();
            
            // 预加载图片数组，确保图片显示
            if (typeof preloadImages === 'function') {
                preloadImages();
            }
            
            // 移除FOUC类以显示内容
            setTimeout(function() {
                document.documentElement.classList.remove('no-fouc');
                document.body.classList.add('content-loaded');
            }, 100);
        });
        
        // 预设内容区域高度以减少布局晃动
        function presetContentHeights() {
            // 设置图片区域最小高度
            var galleryItems = document.querySelectorAll('.gallery-item');
            galleryItems.forEach(function(item) {
                if (!item.style.minHeight) {
                    item.style.minHeight = '100px';
                }
            });
            
            // 设置内容区域最小高度
            var contentArea = document.getElementById('post-content');
            if (contentArea && !contentArea.style.minHeight) {
                contentArea.style.minHeight = '100px';
            }
            
            // 设置联系区域最小高度
            var contactItems = document.querySelectorAll('.contact-item');
            contactItems.forEach(function(item) {
                if (!item.style.minHeight) {
                    item.style.minHeight = '36px';
                }
            });
        }
        
        // 图片延迟加载
        function loadLazyImages() {
            var lazyImages = document.querySelectorAll('.lazy-image');
            
            // 首先设置合理的默认图片路径
            lazyImages.forEach(function(img) {
                // 确保图片源存在
                if (!img.src || img.src === window.location.href) {
                    var defaultPath = img.getAttribute('data-src') || img.src || '';
                    if (defaultPath) {
                        img.src = defaultPath;
                    }
                }
                
                // 初始设置为不透明，避免列表页进入时的闪烁
                img.style.opacity = '0';
                
                // 立即触发加载
                setTimeout(function() {
                    img.style.opacity = '1';
                    img.classList.add('loaded');
                }, 10);
                
                // 当图片加载完成时显示
                img.onload = function() {
                    this.style.opacity = '1';
                    this.classList.add('loaded');
                };
                
                // 设置图片加载失败处理
                img.onerror = function() {
                    if (!this.src.includes('no-image.png')) {
                        this.src = '/static/images/no-image.png';
                    }
                };
            });
        }
    </script>
    
    <!-- 搜索功能脚本 -->
    <script>
        // 搜索功能脚本
        function openSearchLayer() {
            var searchLayer = document.getElementById('searchLayer');
            searchLayer.style.display = 'block';
            setTimeout(function() {
                searchLayer.classList.add('active');
                document.getElementById('searchInput').focus();
            }, 10);
        }
        
        function closeSearchLayer() {
            var searchLayer = document.getElementById('searchLayer');
            searchLayer.classList.remove('active');
            setTimeout(function() {
                searchLayer.style.display = 'none';
            }, 300);
        }

        // 显示搜索加载状态
        function showViewSearchLoading() {
            var searchIcon = document.getElementById('view-search-icon');
            var searchLoading = document.getElementById('view-search-loading');

            if (searchIcon) {
                searchIcon.style.display = 'none';
            }
            if (searchLoading) {
                searchLoading.style.display = 'inline-block';
            }
        }

        // 绑定搜索表单提交事件
        document.addEventListener('DOMContentLoaded', function() {
            var viewSearchForm = document.getElementById('view-search-form');
            if (viewSearchForm) {
                viewSearchForm.addEventListener('submit', function(e) {
                    var input = document.getElementById('searchInput');
                    var keyword = input.value.trim();

                    if (keyword) {
                        showViewSearchLoading();
                    }
                });
            }
        });
    </script>

    <!-- 底部版权 -->
    {include file="footer2.htm"}

    <!-- 底部导航 -->
    <nav class="navbar">
        <a href="/" class="nav-item">
            <span class="nav-icon"><i class="fas fa-home"></i></span>
            <span class="nav-text">首页</span>
        </a>
        <a href="/post.php?category_id={$post.category_id}" class="nav-item">
            <span class="nav-icon"><i class="fas fa-edit"></i></span>
            <span class="nav-text">发布</span>
        </a>
        <a href="javascript:showManage();" class="nav-item">
            <span class="nav-icon"><i class="fas fa-cog"></i></span>
            <span class="nav-text">管理</span>
        </a>
        {if !$isExpired}
        <a href="tel:{$post.mobile}" class="nav-item phone-btn">
            <span class="nav-icon"><i class="fas fa-phone-alt"></i></span>
            <span class="nav-text">拨打电话</span>
        </a>
        <a href="sms:{$post.mobile}" class="nav-item sms-btn">
            <span class="nav-icon"><i class="fas fa-comment-dots"></i></span>
            <span class="nav-text">发送短信</span>
        </a>
        {else}
        <span class="nav-item phone-btn disabled" style="opacity: 0.5; cursor: not-allowed;">
            <span class="nav-icon"><i class="fas fa-phone-alt"></i></span>
            <span class="nav-text">已过期</span>
        </span>
        <span class="nav-item sms-btn disabled" style="opacity: 0.5; cursor: not-allowed;">
            <span class="nav-icon"><i class="fas fa-comment-dots"></i></span>
            <span class="nav-text">已过期</span>
        </span>
        {/if}
    </nav>

    <!-- 管理弹出层 -->
    <div class="manage-layer" id="manageLayer">
        <div class="manage-content-centered">
            <div id="passwordSection" class="password-dialog">
                <h3 class="manage-title">信息管理</h3>
                <p class="manage-subtitle">请输入管理密码以验证您的身份</p>
                <form action="/manage.php" method="post">
                    <input type="hidden" name="id" value="{$post.id}">
                    <div class="password-input-container">
                        <i class="fas fa-lock password-icon"></i>
                        <input type="password" class="manage-input" name="password" placeholder="请输入管理密码" required>
                    </div>
                    <button type="submit" class="manage-btn">验证密码</button>
                    <a href="javascript:;" class="manage-close" onclick="closeManage()">取消</a>
                </form>
            </div>
        </div>
    </div>

    <script>
    var manageLayer = document.getElementById('manageLayer');

    function showManage() {
        if (manageLayer) {
            manageLayer.style.display = 'flex';
            setTimeout(function() {
                manageLayer.classList.add('active');
                // 聚焦到密码输入框
                document.querySelector('.manage-input').focus();
            }, 10);
        }
    }

    function closeManage() {
        if (manageLayer) {
            manageLayer.classList.remove('active');
            setTimeout(function() {
                manageLayer.style.display = 'none';
            }, 300);
        }
    }

    // 点击遮罩层关闭
    if (manageLayer) {
        manageLayer.addEventListener('click', function(e) {
            if (e.target === this) {
                closeManage();
            }
        });
    }

    // 举报相关函数
    function showReportModal(postId) {
        window.currentReportPostId = postId;
        document.getElementById('reportModal').style.display = 'flex';
        // 清空表单
        document.querySelectorAll('input[name="report_type"]').forEach(radio => radio.checked = false);
        document.getElementById('report-content').value = '';
        document.getElementById('report-error').style.display = 'none';
    }

    function closeReportModal() {
        document.getElementById('reportModal').style.display = 'none';
    }

    function submitReport() {
        const reportType = document.querySelector('input[name="report_type"]:checked');
        const content = document.getElementById('report-content').value;
        const errorElement = document.getElementById('report-error');

        if (!reportType) {
            errorElement.textContent = '请选择举报类型';
            errorElement.style.display = 'block';
            return;
        }

        // 提交举报
        fetch('/api/report.php', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
            },
            body: JSON.stringify({
                post_id: window.currentReportPostId,
                report_type: reportType.value,
                content: content
            })
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('举报提交成功，我们会尽快处理');
                closeReportModal();
            } else {
                errorElement.textContent = data.message || '举报提交失败';
                errorElement.style.display = 'block';
            }
        })
        .catch(error => {
            errorElement.textContent = '网络错误，请稍后重试';
            errorElement.style.display = 'block';
        });
    }

    // 点击遮罩层关闭举报弹窗
    document.addEventListener('DOMContentLoaded', function() {
        const reportModal = document.getElementById('reportModal');
        if (reportModal) {
            reportModal.addEventListener('click', function(event) {
                if (event.target === this) {
                    closeReportModal();
                }
            });
        }
    });
    </script>

    <!-- 举报弹出层 -->
    <div class="report-modal" id="reportModal" style="display: none;">
        <div class="report-modal-content">
            <div class="modal-header">
                <h3>举报信息</h3>
                <span class="modal-close" onclick="closeReportModal()">&times;</span>
            </div>
            <div class="modal-body">
                <div class="form-group">
                    <label>举报类型：</label>
                    <div class="report-types">
                        <label><input type="radio" name="report_type" value="1"> 虚假信息</label>
                        <label><input type="radio" name="report_type" value="2"> 诈骗信息</label>
                        <label><input type="radio" name="report_type" value="3"> 违法违规</label>
                        <label><input type="radio" name="report_type" value="4"> 其他问题</label>
                    </div>
                </div>
                <div class="form-group">
                    <label>详细说明（可选）：</label>
                    <textarea id="report-content" placeholder="请简要说明举报原因..." rows="3"></textarea>
                </div>
                <div class="error-message" id="report-error" style="display: none; color: #f00; font-size: 13px; margin-top: 6px;"></div>
            </div>
            <div class="modal-footer">
                <button type="button" onclick="closeReportModal()" class="btn-cancel">取消</button>
                <button type="button" onclick="submitReport()" class="btn-submit">提交举报</button>
            </div>
        </div>
    </div>

    <style>
    /* 举报弹出层样式 */
    .report-modal {
        display: none;
        position: fixed;
        top: 0;
        left: 0;
        width: 100%;
        height: 100%;
        background-color: rgba(0, 0, 0, 0.5);
        z-index: 1000;
        align-items: center;
        justify-content: center;
    }

    .report-modal-content {
        background-color: white;
        border-radius: 8px;
        width: 90%;
        max-width: 380px;
        margin: 20px;
    }

    .modal-header {
        padding: 12px 16px;
        border-bottom: 1px solid #eee;
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    .modal-header h3 {
        margin: 0;
        font-size: 16px;
        color: #333;
    }

    .modal-close {
        font-size: 20px;
        cursor: pointer;
        color: #999;
        line-height: 1;
    }

    .modal-body {
        padding: 16px;
    }

    .form-group {
        margin-bottom: 12px;
    }

    .form-group label {
        display: block;
        margin-bottom: 6px;
        font-weight: 500;
        color: #333;
        font-size: 14px;
    }

    .report-types {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 6px;
    }

    .report-types label {
        display: flex;
        align-items: center;
        padding: 8px 10px;
        border: 1px solid #ddd;
        border-radius: 6px;
        cursor: pointer;
        transition: all 0.2s;
        font-weight: normal;
        font-size: 13px;
    }

    .report-types label:hover {
        background-color: #f5f5f5;
        border-color: var(--primary-color);
    }

    .report-types input[type="radio"] {
        margin-right: 6px;
    }

    .report-types label:has(input:checked) {
        background-color: var(--primary-color-light, #e7f3ff);
        border-color: var(--primary-color);
        color: var(--primary-color);
    }

    #report-content {
        width: 100%;
        padding: 8px;
        border: 1px solid #ddd;
        border-radius: 6px;
        resize: vertical;
        font-family: inherit;
        font-size: 13px;
        box-sizing: border-box;
        min-height: 60px;
    }

    .modal-footer {
        padding: 12px 16px;
        border-top: 1px solid #eee;
        display: flex;
        justify-content: flex-end;
        gap: 8px;
    }

    .btn-cancel, .btn-submit {
        padding: 8px 16px;
        border: none;
        border-radius: 4px;
        cursor: pointer;
        font-size: 14px;
        transition: all 0.2s;
    }

    .btn-cancel {
        background-color: #f5f5f5;
        color: #666;
    }

    .btn-cancel:hover {
        background-color: #e8e8e8;
    }

    .btn-submit {
        background-color: var(--primary-color, #007bff);
        color: white;
    }

    .btn-submit:hover {
        background-color: var(--primary-color-dark, #0056b3);
    }
    </style>

</body>
</html>